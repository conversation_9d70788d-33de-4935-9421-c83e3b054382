#commons
error_lang_invalid=Invalid language parameter
cannot_be_null=\tCannot be empty
number=Number
row=row
error=error
connection_failed=Connection failed
connection_timeout=Connection timeout
delete_fail=Delete fail
start_engine_fail=Start fail
upload_fail=Upload fail
invalid_parameter=Invalid parameter!
name_already_exists=Name already exists
resource_not_exist=The resource does not exist or has been deleted
read_permission_file_fail=Failed to read permission file!
#user related
user_email_already_exists=User email already exists
user_id_is_null=User ID cannot be null
user_name_is_null=User name cannot be null
user_email_is_null=User email cannot be null
password_is_null=Password cannot be null
user_id_already_exists=User ID already exists
password_modification_failed=The old password is wrong. Please re-enter it
cannot_delete_current_user=Cannot delete the user currently logged in
user_already_exists=The user already exists in the current member list
cannot_remove_current=Unable to remove the currently logged in user
password_is_incorrect=Incorrect password or username
user_not_exist=user does not exist：
user_has_been_disabled=the user has been disabled.
excessive_attempts=Excessive attempts
user_locked=the user has been locked.
user_expires=user expires.
not_authorized=not authorized.
login_fail=Login fail
user_apikey_limit=You can create up to 5 API Keys
please_logout_current_user=Please logout current user first
#attachment upload
edit_load_test_not_found=Cannot edit test, test not found=
run_load_test_not_found=Cannot run test, test not found=
run_load_test_file_not_found=Unable to run test, unable to get test file meta information, test ID=
run_load_test_file_content_not_found=Cannot run test, cannot get test file content, test ID=
run_load_test_file_init_error=Failed to run the test, please go to [Settings-System-System Parameter Setting] to check the current site configuration. For details, see https://metersphere.io/docs/faq/load_ test/#url

test_not_found=Test cannot be found:
test_not_running=Test is not running
load_test_already_exists=Duplicate load test name
load_test_name_length=The length of the test name exceeds the limit
no_nodes_message=No node message
duplicate_node_port=Duplicate Ports
duplicate_node_ip_port=Duplicate IPs & Ports
max_thread_insufficient=The number of concurrent users exceeds
related_case_del_fail_prefix=Connected to
related_case_del_fail_suffix=TestCase, please disassociate first
jmx_content_valid=JMX content is invalid
container_delete_fail=The container failed to stop, please try again
#workspace
workspace_name_is_null=Workspace name cannot be null
workspace_name_already_exists=The workspace name already exists
workspace_not_exists=Workspace is not exists
#test resource pool
test_resource_pool_name_is_null=Test Resource Pool name cannot be null
test_resource_pool_name_already_exists=The test resource pool name already exists
load_test=Load Test
test_resource_pool_not_exists=Test resource pool not exists
test_resource_pool_invalid=Test resource pool invalid
#project
project_name_is_null=Project name cannot be null
project_name_already_exists=The project name already exists
project_file_already_exists=The file already exists
project_file_in_use=use this file and cannot be deleted.
#organization
organization_name_already_exists=The organization name already exists
organization_does_not_belong_to_user=The current organization does not belong to the current user
organization_id_is_null=Organization ID cannot be null
#api
api_load_script_error=Load script error
api_test_environment_already_exists="Api test environment already exists"
api_test=API Test
api_versions_update_http=There are multiple versions of this interface. It is not allowed to modify the request type or path. Please create a new interface
api_versions_update=There are multiple versions of this interface, name modification is not allowed, please create a new interface
api_versions_create=The interface already exists, please create a new version at version
#test case
test_case_node_level=level
test_case_node_level_tip=The node tree maximum depth is
test_case_module_not_null=The owned module cannot be empty
test_case_create_module_fail=Failed to create module
test_case_import_template_name=Test_case_templates
test_case_import_template_sheet=Template
module_not_null=The module must not be blank
user_not_exists=The user in this project is not exists
test_case_already_exists=The test case in this project is exists
parse_data_error=Parse data error
missing_header_information=Missing header information
test_case_exist=A test case already exists under this project:
node_deep_limit=The node depth does not exceed 8 layers!
before_delete_plan=There is an associated test case under this plan, please unlink it first!
incorrect_format=\tincorrect format
test_case_name=Name
test_case_type=Type
test_case_maintainer=Maintainer
test_case_priority=Priority
test_case_prerequisite=Prerequisite
test_case_remark=Remark
test_case_step_desc=Step description
test_case_step_result=Step result
test_case_module=Module
test_case=Case
user=User
user_import_template_name=User import templates
user_import_template_sheet=templates
user_import_format_wrong=input error
user_import_id_is_repeat=Id repeat
user_import_email_is_repeat=E-mail repeat
user_import_organization_not_fond=Organization is not found
user_import_workspace_not_fond=Workspace is not found
org_admin=Organization manager
org_member=Organization member
test_manager=Test manager
tester=Tester
read_only_user=Read-only user
module=Module
tag_tip_pattern=Labels should be separated by semicolons or commas
preconditions_optional=Preconditions optional
remark_optional=Remark optional
do_not_modify_header_order=Do not modify the header order
module_created_automatically=If there is no such module, will be created automatically
options=options
options_yes=Yes
options_no=No
required=Required
password_format_is_incorrect=Valid password: 8-30 digits, English upper and lower case letters + numbers + special characters (optional)
please_input_project_member=Please input project merber's number
test_case_report_template_repeat=The workspace has the same name template
plan_name_already_exists=Test plan name already exists
test_case_already_exists_excel=There are duplicate test cases in the import file
test_case_module_already_exists=The module name already exists at the same level
functional_method_tip=Functional test not support auto method
custom_num_is_exist=Use case custom ID already exists
custom_num_is_not_exist=Use case custom ID not exists
id_required=ID required
id_repeat_in_table=ID is repeat in table
step_model_tip=Step description fill in STEP, text description please fill in TEXT (not required)
case_status_not_exist=The use case status must be Prepare, Underway way and Completed
tapd_project_not_exist=The associated TAPD item ID does not exist
zentao_get_project_builds_fail=Get Affecting Version Errors
#ldap
ldap_url_is_null=LDAP address is empty
ldap_dn_is_null=LDAP binding DN is empty
ldap_ou_is_null=LDAP parameter OU is empty
ldap_password_is_null=LDAP password is empty
ldap_connect_fail=Connection LDAP failed
ldap_connect_fail_user=Connection LDAP failed, wrong DN or password bound
ldap_user_filter_is_null=LDAP user filter is empty
ldap_user_mapping_is_null=LDAP user mapping is empty
authentication_failed=User authentication failed,wrong user name or password
user_not_found_or_not_unique=User does not exist or is not unique
ldap_authentication_not_enabled=LDAP authentication is not enabled
login_fail_ou_error=Login failed, please check the user OU
login_fail_filter_error=Login failed, please check the user filter
check_ldap_mapping=Check LDAP attribute mapping
ldap_mapping_value_null=LDAP user attribute mapping field is empty
#quota
quota_project_excess_ws_resource_pool=The resource pool of the project cannot exceed the scope of the resource pool of the workspace
quota_performance_excess_project=The number of performance tests exceeds the project limit
quota_max_threads_excess_project=The maximum concurrent number exceeds the project limit
quota_duration_excess_project=The stress test time exceeds the project limit
quota_member_excess_project=The number of members exceeds the project quota
quota_project_excess_project=Number of projects exceeds workspace quota
import_xmind_count_error=The number of use cases imported into the mind map cannot exceed 800
import_xmind_not_found=Test case not found
license_valid_license_error=Authorization authentication failed
test_review_task_notice=Test review task notice
swagger_url_scheduled_import_notification=SwaggerUrl Scheduled import notification
test_track.length_less_than=The title is too long, the length must be less than
# check owner
check_owner_project=The current user does not have permission to operate this project
check_owner_test=The current user does not have permission to operate this test
check_owner_case=The current user does not have permission to operate this use case
check_owner_plan=The current user does not have permission to operate this plan
check_owner_review=The current user does not have permission to operate this review
check_owner_comment=The current user does not have permission to manipulate this comment
upload_content_is_null=Imported content is empty
test_plan_notification=Test plan notification
task_notification_=Timing task result notification
api_definition_url_not_repeating=The interface request address already exists
api_definition_name_not_repeating=The same name-url combination already exists under the same module
api_definition_name_already_exists=Interface names under the same module cannot be repeated
api_definition_module=The module path is
task_notification_jenkins=Jenkins Task notification
api_definition_name=Interface
task_notification=Result notification
message_task_already_exists=Task recipient already exists
#automation
automation_name_already_exists=the scenario already exists in the module of the same project
automation_name=Scenario
automation_exec_info=There are no test steps to execute
delete_check_reference_by=be referenced by Scenario 
not_execute=Not execute
execute_not_pass=Not pass
execute_pass=Pass
import_fail_custom_num_exists=import fail, custom num is exists
automation_versions_update=There are multiple versions of this scene, name modification is not allowed, please create a new scene
automation_versions_create=The scene already exists, please create a new version at version
#authsource
authsource_name_already_exists=Authentication source name already exists
custom_field_already=A field already exists under this workspace:
expect_name_exists=Expect name is exists
ssl_password_error=The authentication password is wrong, please re-enter the password
ssl_file_error=Failed to load the certification file, please check the certification file
#log
api_definition=Api definition
api_definition_case=Api definition case
api_automation_schedule=Api automation schedule
api_automation=Api automation
api_automation_report=Test Report
track_test_case=Test case
track_test_case_review=Case review
track_test_plan=Test plan
track_test_plan_schedule=Test plan schedule
track_bug=Defect management
track_report=Report
performance_test=Performance test
performance_test_report=Performance test report
system_user=System user
system_organization=System organization
system_workspace=workspace
system_test_resource=System test resource
system_parameter_setting=System parameter setting
system_quota_management=System Quota management
system_authorization_management=System authorization management
organization_member=Organization member
organization_workspace=Organization workspace
project_project_member=Project member
workspace_service_integration=Workspace service integration
workspace_message_settings=Workspace message settings
workspace_member=Workspace member
workspace_template_settings_field=Workspace template settings field
workspace_template_settings_case=Workspace template settings case
workspace_template_settings_issue=Workspace template settings issue
project_project_manager=Project project manager
project_project_jar=Project project jar
project_environment_setting=Project environment setting
project_file_management=Project file management
personal_information_personal_settings=Personal information personal settings
personal_information_apikeys=Personal information API Keys
auth_title=Auth
group_permission=Group
test_case_status_prepare=Prepare
test_case_status_running=Running
test_case_status_finished=Finished
test_case_status_error=Error
test_case_status_success=Success
test_case_status_trash=Trash
test_case_status_saved=Saved
connection_expired=The connection has expired, please get it again
# track home
api_case=Api
performance_case=Performance
scenario_case=Scenario
create_user=Create user
test_case_status=Case status
id_not_rightful=ID is not rightful
project_reference_multiple_plateform=Projects point to multiple third-party platforms
# mock
mock_warning=No matching Mock expectation was found
#项目报告
enterprise_test_report=Enterprise report
count=Count
cannot_find_project=Cannot find project
project_repeatable_is_false=Url repeatable not open
#环境组
null_environment_group_name=Environment group name is null
environment_group_name=Environment group name
environment_group_exist=already exists
environment_group_has_duplicate_project=Environment group has duplicate project
environment_group=Environment group
#误报库
error_report_library=Error report
issue_jira_info_error=Check the service integration information or Jira project ID
error_code_is_unique=Error code is not unique
no_version_exists=version not exists
jira_auth_error=Account name or password (Token) is wrong
jira_auth_url_error=The test connection failed, please check whether the Jira address is correct
#ui 指令校驗
is_null=can't be null
url_is_null=URL can't be null
locator_is_null=locator can't be null
coord=coord
input_content=input
subitem_type=subitem type
subitem=subitem
varname=variable
attributeName=attribute name
expression=expression
times=times
command=command
extract_type=extract type
cmdValidation=validation
cmdValidateValue=validate value
cmdValidateText=validate text
cmdValidateDropdown=validate dropdown
cmdValidateElement=validate element
cmdValidateTitle=validate title
cmdOpen=open
cmdSelectWindow=select window
cmdSetWindowSize=set window size
cmdSelectFrame=select frame
cmdDialog=dialog operation
cmdDropdownBox=dropdown
submit=submit
cmdSetItem=set item
cmdWaitElement=wait element
cmdInput=input
cmdMouseClick=click
cmdMouseMove=mouse move
cmdMouseDrag=mouse drag
cmdTimes=times
cmdForEach=ForEach
cmdWhile=While
cmdIf=If
cmdElse=Else
cmdElseIf=ElseIf
close=close
cmdExtraction=extracion
cmdExtractWindow=window extraction
cmdExtractElement=element extraction
tcp_mock_not_unique=This tcp port is be used
report_warning=Report Type and Report ID cannot be empty
report_type_error=report type error
serial=Serial
parallel=Parallel
csv_no_exist=CSV file does not exist
update_scenario=Updated the scene
scenario_update_notice=Interface Automation Notification
create_scenario=new scene
scenario_create_notice=Interface Automation Notification
update_api=Updated interface definition
api_update_notice=Interface update notification
create_api=New interface definition
api_create_notice=Interface creation notification
create_api_case=New interface use case
api_case_create_notice=Interface use case new notification
update_api_case=Updated interface use case
api_case_update_notice=Interface use case update notification
error_xml_struct=Data is not xml
case_name_is_already_exist=Duplicate Case name
file_format_does_not_meet_requirements=File format does not meet requirements
url_is_not_valid=URL is not valid
scenario_step_parsing_error_check=Scenario step parsing error, check if plugin step is included!
pre_processor_env=global prescript
post_processor_env=global postscript
scenario_warning=The scene contains plugin steps, the plugin has been deleted and cannot be exported