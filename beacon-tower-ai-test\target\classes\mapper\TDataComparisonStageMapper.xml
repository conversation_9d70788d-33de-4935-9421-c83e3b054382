<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kf.aitest.dao.TDataComparisonStageMapper">

    <!-- 独立任务列表DTO结果映射 -->
    <resultMap id="IndependentTaskListDTOResultMap" type="com.kf.aitest.dto.IndependentTaskListDTO">
        <result column="task_id" property="taskId" jdbcType="VARCHAR"/>
        <result column="file_md5" property="fileMd5" jdbcType="VARCHAR"/>
        <result column="stage_name" property="stage" jdbcType="VARCHAR"/>
        <result column="stage_status" property="status" jdbcType="VARCHAR"/>
        <result column="ai_score" property="aiScore" jdbcType="INTEGER"/>
        <result column="duration" property="duration" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="data_type" property="dataType" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 分页查询独立任务列表 -->
    <select id="selectIndependentTaskList" resultMap="IndependentTaskListDTOResultMap">
        SELECT
            task_id,
            file_md5,
            stage_name,
            stage_status,
            ai_score,
            duration,
            create_time,
            update_time,
            error_message,
            data_type
        FROM t_data_comparison_stage
        WHERE 1=1
            <if test="fileMd5 != null and fileMd5 != ''">
                AND file_md5 LIKE CONCAT('%', #{fileMd5}, '%')
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        ORDER BY create_time DESC
    </select>

    <!-- 注意：countTaskList查询已删除，MyBatis-Plus会自动处理分页计数 -->

    <!-- 根据任务ID查询所有阶段结果 -->
    <select id="selectByTaskId" resultType="com.kf.aitest.entity.TDataComparisonStage">
        SELECT * FROM t_data_comparison_stage
        WHERE task_id = #{taskId}
        ORDER BY data_id, stage_name
    </select>

    <!-- 根据任务ID和阶段名称查询阶段结果 -->
    <select id="selectByTaskIdAndStage" resultType="com.kf.aitest.entity.TDataComparisonStage">
        SELECT * FROM t_data_comparison_stage
        WHERE task_id = #{taskId} AND stage_name = #{stageName}
        LIMIT 1
    </select>

    <!-- 注意：selectByDataId查询已移除，因为在扁平化处理模式下不再需要按dataId查询 -->

    <!-- 批量插入阶段结果 -->
    <insert id="batchInsert">
        INSERT INTO t_data_comparison_stage (
            task_id, file_md5, stage_name, data_type, stage_status,
            error_message, duration, fetch_time, ai_evaluation, ai_score,
            uat_data, test_data, create_time, update_time
        ) VALUES
        <foreach collection="stageList" item="stage" separator=",">
            (
                #{stage.taskId}, #{stage.fileMd5}, #{stage.stageName},
                #{stage.dataType}, #{stage.stageStatus}, #{stage.errorMessage},
                #{stage.duration}, #{stage.fetchTime}, #{stage.aiEvaluation},
                #{stage.aiScore}, #{stage.uatData}, #{stage.testData},
                #{stage.createTime}, #{stage.updateTime}
            )
        </foreach>
    </insert>

    <!-- 统计各阶段的成功率 -->
    <select id="selectStageStatistics" resultType="com.kf.aitest.entity.TDataComparisonStage">
        SELECT
            stage_name,
            COUNT(*) as total_count,
            SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
            ROUND(SUM(CASE WHEN stage_status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
        FROM t_data_comparison_stage
        WHERE task_id = #{taskId}
        GROUP BY stage_name
        ORDER BY stage_name
    </select>

    <!-- 查询失败的阶段结果 -->
    <select id="selectFailedStages" resultType="com.kf.aitest.entity.TDataComparisonStage">
        SELECT * FROM t_data_comparison_stage
        WHERE task_id = #{taskId} AND stage_status IN ('FAILED', 'TIMEOUT')
        ORDER BY data_id, stage_name
    </select>

</mapper>
