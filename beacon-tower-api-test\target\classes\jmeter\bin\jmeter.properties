################################################################################
# Apache JMeter Property file
################################################################################

##   Licensed to the Apache Software Foundation (ASF) under one or more
##   contributor license agreements.  See the NOTICE file distributed with
##   this work for additional information regarding copyright ownership.
##   The ASF licenses this file to You under the Apache License, Version 2.0
##   (the "License"); you may not use this file except in compliance with
##   the License.  You may obtain a copy of the License at
##
##       http://www.apache.org/licenses/LICENSE-2.0
##
##   Unless required by applicable law or agreed to in writing, software
##   distributed under the License is distributed on an "AS IS" BASIS,
##   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
##   See the License for the specific language governing permissions and
##   limitations under the License.

################################################################################
#
#                      THIS FILE SHOULD NOT BE MODIFIED
#
# This avoids having to re-apply the modifications when upgrading JMeter
# Instead only user.properties should be modified:
# 1/ copy the property you want to modify to user.properties from jmeter.properties
# 2/ Change its value there
#
################################################################################

# JMeter properties are described in the file
# http://jmeter.apache.org/usermanual/properties_reference.html
# A local copy can be found in
# printable_docs/usermanual/properties_reference.html

#Preferred GUI language. Comment out to use the JVM default locale's language.
#language=en


# Additional locale(s) to add to the displayed list.
# The current default list is: en, fr, de, no, es, tr, ja, zh_CN, zh_TW, pl, pt_BR
# [see JMeterMenuBar#makeLanguageMenu()]
# The entries are a comma-separated list of language names
#locales.add=zu


#---------------------------------------------------------------------------
# XML Parser
#---------------------------------------------------------------------------

# Path to a Properties file containing Namespace mapping in the form
# prefix=Namespace
# Example:
# ns=http://biz.aol.com/schema/2006-12-18
#xpath.namespace.config=


# XPath2 query cache for storing compiled XPath queries
#xpath2query.parser.cache.size=400

#---------------------------------------------------------------------------
# SSL configuration
#---------------------------------------------------------------------------

## SSL System properties are now in system.properties

# JMeter no longer converts javax.xxx property entries in this file into System properties.
# These must now be defined in the system.properties file or on the command-line.
# The system.properties file gives more flexibility.

# By default, SSL session contexts are now created per-thread, rather than being shared.
# The original behaviour can be enabled by setting the JMeter property to true
#https.sessioncontext.shared=false

# Be aware that https default protocol may vary depending on the version of JVM
# See https://blogs.oracle.com/java-platform-group/entry/diagnosing_tls_ssl_and_https
# See https://bz.apache.org/bugzilla/show_bug.cgi?id=58236
# Default HTTPS protocol level:
#https.default.protocol=TLS
# This may need to be changed here (or in user.properties) to:
#https.default.protocol=SSLv3

# List of protocols to enable. You may have to select only a subset if you find issues with target server.
# This is needed when server does not support Socket version negotiation, this can lead to:
# javax.net.ssl.SSLPeerUnverifiedException: peer not authenticated
# java.net.SocketException: Connection reset
# see https://bz.apache.org/bugzilla/show_bug.cgi?id=54759
#https.socket.protocols=SSLv2Hello SSLv3 TLSv1

# Control if we allow reuse of cached SSL context between iterations
# set the value to 'false' to reset the SSL context each iteration
# Deprecated since 5.0
#https.use.cached.ssl.context=true

#
# Reset HTTP State when starting a new Thread Group iteration which means:
# true means next iteration is associated to a new user
# false means next iteration is associated to same user
# true involves:
# - Closing opened connection
# - resetting SSL State
#httpclient.reset_state_on_thread_group_iteration=true

# Start and end index to be used with keystores with many entries
# The default is to use entry 0, i.e. the first
#https.keyStoreStartIndex=0
#https.keyStoreEndIndex=0

#---------------------------------------------------------------------------
# Look and Feel configuration
#---------------------------------------------------------------------------

#Classname of the Swing default UI
#
# The LAF classnames that are available are now displayed as ToolTip text
# when hovering over the Options/Look and Feel selection list.
#
# You can either use a full class name, as shown below,
# or one of the strings "System" or "CrossPlatform" which means
#  JMeter will use the corresponding string returned by UIManager.get<name>LookAndFeelClassName()

# LAF can be overridden by os.name (lowercased, spaces replaced by '_')
# Sample os.name LAF:
#jmeter.laf.windows_xp=javax.swing.plaf.metal.MetalLookAndFeel

# Failing that, the OS family = os.name, but only up to first space:
# Sample OS family LAF:
#jmeter.laf.windows=com.sun.java.swing.plaf.windows.WindowsLookAndFeel

# Custom settings for Mac using System LAF if you don't want to use Dracula
#jmeter.laf.mac=System

# Failing that, the JMeter default laf can be defined:
#jmeter.laf=System

# If none of the above jmeter.laf properties are defined, JMeter uses the CrossPlatform LAF.
# This is because the CrossPlatform LAF generally looks better than the System LAF.
# See https://bz.apache.org/bugzilla/show_bug.cgi?id=52026 for details
# N.B. the laf can be defined in user.properties.

# LoggerPanel display
# default to false
#jmeter.loggerpanel.display=false

# Enable LogViewer Panel to receive log event even if closed
# Enabled since 2.12
# Note this has some impact on performances, but as GUI mode must
# not be used for Load Test it is acceptable
#jmeter.loggerpanel.enable_when_closed=true

# Max lines kept in LoggerPanel, default to 1000 chars
# 0 means no limit
#jmeter.loggerpanel.maxlength=1000

# Interval period in ms to process the queue of events of the listeners
#jmeter.gui.refresh_period=500

# HiDPI mode (default: false)
# Activate a 'pseudo'-hidpi mode. Allows to increase size of some UI elements
# which are not correctly managed by JVM with high resolution screens in Linux or Windows
#jmeter.hidpi.mode=false
# To enable pseudo-hidpi mode change to true
#jmeter.hidpi.mode=true
# HiDPI scale factor
#jmeter.hidpi.scale.factor=1.0
# Suggested value for HiDPI
#jmeter.hidpi.scale.factor=2.0

# Toolbar display
# Toolbar icon definitions
#jmeter.toolbar.icons=org/apache/jmeter/images/toolbar/icons-toolbar.properties
# Toolbar list
#jmeter.toolbar=new,open,close,save,save_as_testplan,|,cut,copy,paste,|,expand,collapse,toggle,|,test_start,test_stop,test_shutdown,|,test_start_remote_all,test_stop_remote_all,test_shutdown_remote_all,|,test_clear,test_clear_all,|,search,search_reset,|,function_helper,help
# Toolbar icons default size: 22x22. Available sizes are: 22x22, 32x32, 48x48
#jmeter.toolbar.icons.size=22x22
# Suggested value for HiDPI
#jmeter.toolbar.icons.size=48x48

# Icon definitions
# default:
#jmeter.icons=org/apache/jmeter/images/icon.properties
# alternate:
#jmeter.icons=org/apache/jmeter/images/icon_1.properties
# Historical icon set (deprecated)
#jmeter.icons=org/apache/jmeter/images/icon_old.properties

# Tree icons default size: 19x19. Available sizes are: 19x19, 24x24, 32x32, 48x48
# Useful for HiDPI display (see below)
#jmeter.tree.icons.size=19x19
# Suggested value for HiDPI screen like 3200x1800:
#jmeter.tree.icons.size=32x32

#Components to not display in JMeter GUI (GUI class name or static label)
# These elements are deprecated and will be removed in next version:
# MongoDB Script, MongoDB Source Config, Monitor Results
# BSF Elements
not_in_menu=org.apache.jmeter.protocol.mongodb.sampler.MongoScriptSampler,org.apache.jmeter.protocol.mongodb.config.MongoSourceElement,\
    org.apache.jmeter.timers.BSFTimer,org.apache.jmeter.modifiers.BSFPreProcessor,org.apache.jmeter.extractor.BSFPostProcessor,org.apache.jmeter.assertions.BSFAssertion,\
    org.apache.jmeter.visualizers.BSFListener,org.apache.jmeter.protocol.java.sampler.BSFSampler,\
    org.apache.jmeter.protocol.http.control.gui.SoapSamplerGui

# Number of items in undo history
# Feature is disabled by default (0) due to known and not fixed bugs:
# https://bz.apache.org/bugzilla/show_bug.cgi?id=57043
# https://bz.apache.org/bugzilla/show_bug.cgi?id=57039
# https://bz.apache.org/bugzilla/show_bug.cgi?id=57040
# Set it to a number > 0 (25 can be a good default)
# The bigger it is, the more it consumes memory
#undo.history.size=0

# Hotkeys to add JMeter components, will add elements when you press Ctrl+0 .. Ctrl+9 (Command+0 .. Command+9 on Mac)
gui.quick_0=ThreadGroupGui
gui.quick_1=HttpTestSampleGui
gui.quick_2=RegexExtractorGui
gui.quick_3=AssertionGui
gui.quick_4=ConstantTimerGui
gui.quick_5=TestActionGui
gui.quick_6=JSR223PostProcessor
gui.quick_7=JSR223PreProcessor
gui.quick_8=DebugSampler
gui.quick_9=ViewResultsFullVisualizer


#---------------------------------------------------------------------------
# JMX Backup configuration
#---------------------------------------------------------------------------
#Enable auto backups of the .jmx file when a test plan is saved.
#When enabled, before the .jmx is saved, it will be backed up to the directory pointed
#by the jmeter.gui.action.save.backup_directory property (see below). Backup file names are built
#after the jmx file being saved. For example, saving test-plan.jmx will create a test-plan-000012.jmx
#in the backup directory provided that the last created backup file is test-plan-000011.jmx.
#Default value is true indicating that auto backups are enabled
#jmeter.gui.action.save.backup_on_save=true

#Set the backup directory path where JMX backups will be created upon save in the GUI.
#If not set (what it defaults to) then backup files will be created in
#a sub-directory of the JMeter base installation. The default directory is ${JMETER_HOME}/backups
#If set and the directory does not exist, it will be created.
#jmeter.gui.action.save.backup_directory=

#Set the maximum time (in hours) that backup files should be preserved since the save time.
#By default no expiration time is set which means we keep backups for ever.
#jmeter.gui.action.save.keep_backup_max_hours=0

#Set the maximum number of backup files that should be preserved. By default 10 backups will be preserved.
#Setting this to zero will cause the backups to not being deleted (unless keep_backup_max_hours is set to a non zero value)
#jmeter.gui.action.save.keep_backup_max_count=10

#Enable auto saving of the .jmx file before start run a test plan
#When enabled, before the run, the .jmx will be saved and also backed up to the directory pointed
#save_automatically_before_run=true

#---------------------------------------------------------------------------
# Remote hosts and RMI configuration
#---------------------------------------------------------------------------

# Remote Hosts - comma delimited
remote_hosts=127.0.0.1
#remote_hosts=localhost:1099,localhost:2010

# RMI port to be used by the server (must start rmiregistry with same port)
#server_port=1099

# To change the port to (say) 1234:
# On the server(s)
# - set server_port=1234
# - start rmiregistry with port 1234
# On Windows this can be done by:
# SET SERVER_PORT=1234
# JMETER-SERVER
#
# On Unix:
# SERVER_PORT=1234 jmeter-server
#
# On the client:
# - set remote_hosts=server:1234

# Parameter that controls the RMI port used by RemoteSampleListenerImpl (The Controller)
# Default value is 0 which means port is randomly assigned
# You may need to open Firewall port on the Controller machine
#client.rmi.localport=0

# When distributed test is starting, there may be several attempts to initialize
# remote engines. By default, only single try is made. Increase following property
# to make it retry for additional times
#client.tries=1

# If there is initialization retries, following property sets delay between attempts
#client.retries_delay=5000

# When all initialization tries was made, test will fail if some remote engines are failed
# Set following property to true to ignore failed nodes and proceed with test
#client.continue_on_fail=false

# To change the default port (1099) used to access the server:
#server.rmi.port=1234

# To use a specific port for the JMeter server engine, define
# the following property before starting the server:
#server.rmi.localport=4000

# The jmeter server creates by default the RMI registry as part of the server process.
# To stop the server creating the RMI registry:
#server.rmi.create=false

# Define the following property to cause JMeter to exit after the first test
#server.exitaftertest=true

#
# Configuration of Secure RMI connection
#
# Type of keystore : JKS
#server.rmi.ssl.keystore.type=JKS
#
# Keystore file that contains private key
#server.rmi.ssl.keystore.file=rmi_keystore.jks
#
# Password of Keystore
#server.rmi.ssl.keystore.password=changeit
#
# Key alias
#server.rmi.ssl.keystore.alias=rmi
#
# Type of truststore : JKS
#server.rmi.ssl.truststore.type=JKS
#
# Keystore file that contains certificate
#server.rmi.ssl.truststore.file=rmi_keystore.jks
#
# Password of Trust store
#server.rmi.ssl.truststore.password=changeit
#
# Set this if you don't want to use SSL for RMI
#server.rmi.ssl.disable=false
#---------------------------------------------------------------------------
#         Include Controller
#---------------------------------------------------------------------------

# Prefix used by IncludeController when building file name
#includecontroller.prefix=

#---------------------------------------------------------------------------
# Shared HTTP configuration between HC4 and Java Implementations
#---------------------------------------------------------------------------

#
# Should JMeter add to POST request content-type header if missing:
# Content-Type: application/x-www-form-urlencoded
# Was true before version 5.0
#post_add_content_type_if_missing=false

#---------------------------------------------------------------------------
# HTTP Java configuration
#---------------------------------------------------------------------------

# Number of connection retries performed by HTTP Java sampler before giving up
# 0 means no retry since version 3.0
#http.java.sampler.retries=0

#---------------------------------------------------------------------------
# Following properties apply to Apache HttpClient
#---------------------------------------------------------------------------

# set the socket timeout (or use the parameter http.socket.timeout)
# for AJP Sampler implementation.
# Value is in milliseconds
#httpclient.timeout=0
# 0 == no timeout

# Set the http version (defaults to 1.1)
#httpclient.version=1.1 (or use the parameter http.protocol.version)

# Define characters per second > 0 to emulate slow connections
#httpclient.socket.http.cps=0
#httpclient.socket.https.cps=0

#Enable loopback protocol
#httpclient.loopback=true

# Define the local host address to be used for multi-homed hosts
#httpclient.localaddress=*******

#---------------------------------------------------------------------------
# AuthManager Kerberos configuration
#---------------------------------------------------------------------------

# AuthManager Kerberos configuration
# Name of application module used in jaas.conf
#kerberos_jaas_application=JMeter

# Should ports be stripped from urls before constructing SPNs
# for SPNEGO authentication
#kerberos.spnego.strip_port=true

# Should credentials be delegated to webservers when using
# SPNEGO authentication
#kerberos.spnego.delegate_cred=false

#---------------------------------------------------------------------------
# Apache HttpComponents HTTPClient configuration (HTTPClient4)
#---------------------------------------------------------------------------

# define a properties file for overriding Apache HttpClient parameters
# Uncomment this line if you put anything in hc.parameters file
#hc.parameters.file=hc.parameters

# If true, default HC4 User-Agent will not be added
#httpclient4.default_user_agent_disabled=false

# Preemptively send Authorization Header when BASIC auth is used
#httpclient4.auth.preemptive=true

# Number of retries to attempt (default 0)
#httpclient4.retrycount=0

# true if it's OK to retry requests that have been sent
# This will retry Idempotent and non Idempotent requests
# This should usually be false, but it can be useful
# when testing against some Load Balancers like Amazon ELB
#httpclient4.request_sent_retry_enabled=false

# Idle connection timeout (Milliseconds) to apply if the server does not send
# Keep-Alive headers (default 0)
# Set this > 0 to compensate for servers that don't send a Keep-Alive header
# If <= 0, idle timeout will only apply if the server sends a Keep-Alive header
#httpclient4.idletimeout=0

# Check connections if the elapsed time (Milliseconds) since the last
# use of the connection exceed this value
#httpclient4.validate_after_inactivity=1700

# TTL (in Milliseconds) represents an absolute value.
# No matter what, the connection will not be re-used beyond its TTL.
#httpclient4.time_to_live=2000

# Max size in bytes of PUT body to retain in result sampler.
# Bigger results will be clipped.
#httpclient4.max_body_retain_size=32768

# Ignore EOFException that some edgy application may emit to signal end of GZIP stream
# Defaults to false
#httpclient4.gzip_relax_mode=false

# Ignore EOFException that some edgy application may emit to signal end of Deflated stream
# Defaults to false
#httpclient4.deflate_relax_mode=false

#---------------------------------------------------------------------------
# HTTP Cache Manager configuration
#---------------------------------------------------------------------------
#
# Space or comma separated list of methods that can be cached
#cacheable_methods=GET
# N.B. This property is currently a temporary solution for Bug 56162

# Since 2.12, JMeter does not create anymore a Sample Result with 204 response
# code for a resource found in cache which is inline with what browser do.
#cache_manager.cached_resource_mode=RETURN_NO_SAMPLE

# You can choose between 3 modes:
# RETURN_NO_SAMPLE (default)
# RETURN_200_CACHE
# RETURN_CUSTOM_STATUS

# Those mode have the following behaviours:
# RETURN_NO_SAMPLE:
# this mode returns no Sample Result, it has no additional configuration

# RETURN_200_CACHE:
# this mode will return Sample Result with response code to 200 and
# response message to "(ex cache)", you can modify response message by setting
# RETURN_200_CACHE.message=(ex cache)

# RETURN_CUSTOM_STATUS:
# This mode lets you select what response code and message you want to return,
# if you use this mode you need to set those properties
# RETURN_CUSTOM_STATUS.code=
# RETURN_CUSTOM_STATUS.message=

#---------------------------------------------------------------------------
# Results file configuration
#---------------------------------------------------------------------------

# This section helps determine how result data will be saved.
# The commented out values are the defaults.

# legitimate values: xml, csv, db.  Only xml and csv are currently supported.
#jmeter.save.saveservice.output_format=csv

# The below properties are true when field should be saved; false otherwise
#
# assertion_results_failure_message only affects CSV output
#jmeter.save.saveservice.assertion_results_failure_message=true
#
# legitimate values: none, first, all
#jmeter.save.saveservice.assertion_results=none
#
#jmeter.save.saveservice.data_type=true
#jmeter.save.saveservice.label=true
#jmeter.save.saveservice.response_code=true
# response_data is not currently supported for CSV output
#jmeter.save.saveservice.response_data=false
# Save ResponseData for failed samples
#jmeter.save.saveservice.response_data.on_error=false
#jmeter.save.saveservice.response_message=true
#jmeter.save.saveservice.successful=true
#jmeter.save.saveservice.thread_name=true
#jmeter.save.saveservice.time=true
#jmeter.save.saveservice.subresults=true
#jmeter.save.saveservice.assertions=true
#jmeter.save.saveservice.latency=true
# Only available with HttpClient4
#jmeter.save.saveservice.connect_time=true
#jmeter.save.saveservice.samplerData=false
#jmeter.save.saveservice.responseHeaders=false
#jmeter.save.saveservice.requestHeaders=false
#jmeter.save.saveservice.encoding=false
#jmeter.save.saveservice.bytes=true
# Only available with HttpClient4
#jmeter.save.saveservice.sent_bytes=true
#jmeter.save.saveservice.url=true
#jmeter.save.saveservice.filename=false
#jmeter.save.saveservice.hostname=false
#jmeter.save.saveservice.thread_counts=true
#jmeter.save.saveservice.sample_count=false
#jmeter.save.saveservice.idle_time=true

# Timestamp format - this only affects CSV output files
# legitimate values: none, ms, or a format suitable for SimpleDateFormat
#jmeter.save.saveservice.timestamp_format=ms
#jmeter.save.saveservice.timestamp_format=yyyy/MM/dd HH:mm:ss.SSS

# For use with Comma-separated value (CSV) files or other formats
# where the fields' values are separated by specified delimiters.
# Default:
#jmeter.save.saveservice.default_delimiter=,
# For TAB, one can use:
#jmeter.save.saveservice.default_delimiter=\t

# Only applies to CSV format files:
# Print field names as first line in CSV
#jmeter.save.saveservice.print_field_names=true

# Optional list of JMeter variable names whose values are to be saved in the result data files.
# Use commas to separate the names. For example:
#sample_variables=SESSION_ID,REFERENCE
# N.B. The current implementation saves the values in XML as attributes,
# so the names must be valid XML names.
# By default JMeter sends the variable to all servers
# to ensure that the correct data is available at the client.

# Optional xml processing instruction for line 2 of the file:
# Example:
#jmeter.save.saveservice.xml_pi=<?xml-stylesheet type="text/xsl" href="../extras/jmeter-results-detail-report.xsl"?>
# Default value:
#jmeter.save.saveservice.xml_pi=

# Prefix used to identify filenames that are relative to the current base
#jmeter.save.saveservice.base_prefix=~/

# AutoFlush on each line written in XML or CSV output
# Setting this to true will result in less test results data loss in case of Crash
# but with impact on performances, particularly for intensive tests (low or no pauses)
# Since JMeter 2.10, this is false by default
#jmeter.save.saveservice.autoflush=false

#---------------------------------------------------------------------------
# Settings that affect SampleResults
#---------------------------------------------------------------------------

# Save the start time stamp instead of the end
# This also affects the timestamp stored in result files
sampleresult.timestamp.start=true

# Whether to use System.nanoTime() - otherwise only use System.currentTimeMillis()
#sampleresult.useNanoTime=true

# Use a background thread to calculate the nanoTime offset
# Set this to <= 0 to disable the background thread
#sampleresult.nanoThreadSleep=5000

# Since version 5.0 JMeter has a new SubResult Naming Policy which numbers subresults by default
# This property if set to true discards renaming policy. This can be required if you're using JMeter for functional testing.
# Defaults to: false
#subresults.disable_renaming=false

#---------------------------------------------------------------------------
# Upgrade property
#---------------------------------------------------------------------------

# File that holds a record of name changes for backward compatibility issues
upgrade_properties=/bin/upgrade.properties

#---------------------------------------------------------------------------
# JMeter Test Script recorder configuration
#
# N.B. The element was originally called the Proxy recorder, which is why the
# properties have the prefix "proxy".
#---------------------------------------------------------------------------

# If the recorder detects a gap of at least 5s (default) between HTTP requests,
# it assumes that the user has clicked a new URL
#proxy.pause=5000

# Add numeric suffix to Sampler names (default true)
#proxy.number.requests=true

# List of URL patterns that will be added to URL Patterns to exclude
# Separate multiple lines with ;
#proxy.excludes.suggested=.*\\.(bmp|css|js|gif|ico|jpe?g|png|swf|woff|woff2)

# Change the default HTTP Sampler (currently HttpClient4)
# Java:
#jmeter.httpsampler=HTTPSampler
#or
#jmeter.httpsampler=Java
#
# HttpClient4.x
#jmeter.httpsampler=HttpClient4

# By default JMeter tries to be more lenient with RFC2616 redirects and allows
# relative paths.
# If you want to test strict conformance, set this value to true
# When the property is true, JMeter follows http://tools.ietf.org/html/rfc3986#section-5.2
#jmeter.httpclient.strict_rfc2616=false

# Default content-type include filter to use
#proxy.content_type_include=text/html|text/plain|text/xml
# Default content-type exclude filter to use
#proxy.content_type_exclude=image/.*|text/css|application/.*

# Default headers to remove from Header Manager elements
# (Cookie and Authorization are always removed)
#proxy.headers.remove=If-Modified-Since,If-None-Match,Host

# Binary content-type handling
# These content-types will be handled by saving the request in a file:
#proxy.binary.types=application/x-amf,application/x-java-serialized-object,binary/octet-stream
# The files will be saved in this directory:
#proxy.binary.directory=user.dir
# The files will be created with this file filesuffix:
#proxy.binary.filesuffix=.binary

#---------------------------------------------------------------------------
# Test Script Recorder certificate configuration
#---------------------------------------------------------------------------

#proxy.cert.directory=<JMeter bin directory>
#proxy.cert.file=proxyserver.jks
#proxy.cert.type=JKS
#proxy.cert.keystorepass=password
#proxy.cert.keypassword=password
#proxy.cert.factory=SunX509
# define this property if you wish to use your own keystore
#proxy.cert.alias=<none>
# The default validity for certificates created by JMeter
#proxy.cert.validity=7
# Use dynamic key generation (if supported by JMeter/JVM)
# If false, will revert to using a single key with no certificate
#proxy.cert.dynamic_keys=true

#---------------------------------------------------------------------------
# Test Script Recorder miscellaneous configuration
#---------------------------------------------------------------------------

# Whether to attempt disabling of samples that resulted from redirects
# where the generated samples use auto-redirection
#proxy.redirect.disabling=true

# SSL configuration
#proxy.ssl.protocol=TLS

#---------------------------------------------------------------------------
# JMeter Proxy configuration
#---------------------------------------------------------------------------
# use command-line flags for user-name and password
#http.proxyDomain=NTLM domain, if required by HTTPClient sampler

#---------------------------------------------------------------------------
# HTTPSampleResponse Parser configuration
#---------------------------------------------------------------------------

# Space-separated list of parser groups
HTTPResponse.parsers=htmlParser wmlParser cssParser
# for each parser, there should be a parser.types and a parser.className property

# CSS Parser based on ph-css
cssParser.className=org.apache.jmeter.protocol.http.parser.CssParser
cssParser.types=text/css

# CSS parser LRU cache size
# This cache stores the URLs found in a CSS to avoid continuously parsing the CSS
# By default the cache size is 400
# It can be disabled by setting its value to 0
#css.parser.cache.size=400

# Let the CSS Parser ignore all css errors
#css.parser.ignore_all_css_errors=true

#---------------------------------------------------------------------------
# HTML Parser configuration
#---------------------------------------------------------------------------

# Define the HTML parser to be used.
# Default parser:
# This new parser (since 2.10) should perform better than all others
# see https://bz.apache.org/bugzilla/show_bug.cgi?id=55632
# Do not comment this property
htmlParser.className=org.apache.jmeter.protocol.http.parser.LagartoBasedHtmlParser

# Other parsers:
# Default parser before 2.10
#htmlParser.className=org.apache.jmeter.protocol.http.parser.JTidyHTMLParser
# Note that Regexp extractor may detect references that have been commented out.
# In many cases it will work OK, but you should be aware that it may generate
# additional references.
#htmlParser.className=org.apache.jmeter.protocol.http.parser.RegexpHTMLParser
# This parser is based on JSoup, it should be the most accurate but less
# performant than LagartoBasedHtmlParser
#htmlParser.className=org.apache.jmeter.protocol.http.parser.JsoupBasedHtmlParser

#Used by HTTPSamplerBase to associate htmlParser with content types below
htmlParser.types=text/html application/xhtml+xml application/xml text/xml

#---------------------------------------------------------------------------
# WML Parser configuration
#---------------------------------------------------------------------------

wmlParser.className=org.apache.jmeter.protocol.http.parser.RegexpHTMLParser

#Used by HTTPSamplerBase to associate wmlParser with content types below
wmlParser.types=text/vnd.wap.wml

#---------------------------------------------------------------------------
# Remote batching configuration
#---------------------------------------------------------------------------
# How is Sample sender implementations configured:
# - true (default) means client configuration will be used
# - false means server configuration will be used
#sample_sender_client_configured=true

# By default when Stripping modes are used JMeter since 3.1 will strip
# response even for SampleResults in error.
# If you want to revert to previous behaviour (no stripping of Responses in error)
# set this property to false
#sample_sender_strip_also_on_error=true

# Remote batching support
# Since JMeter 2.9, default is MODE_STRIPPED_BATCH, which returns samples in
# batch mode (every 100 samples or every minute by default)
# Note also that MODE_STRIPPED_BATCH strips response data from SampleResult, so if you need it change to
# another mode
# Batch returns samples in batches
# Statistical returns sample summary statistics
# mode can also be the class name of an implementation of org.apache.jmeter.samplers.SampleSender
#mode=Standard
#mode=Batch
#mode=Statistical
#Set to true to key statistical samples on threadName rather than threadGroup
#key_on_threadname=false
#mode=Stripped
#mode=StrippedBatch
#mode=org.example.load.MySampleSender
#
#num_sample_threshold=100
# Value is in milliseconds
#time_threshold=60000
#
# Asynchronous sender; uses a queue and background worker process to return the samples
#mode=Asynch
# default queue size
#asynch.batch.queue.size=100
# Same as Asynch but strips response data from SampleResult
#mode=StrippedAsynch
#
# DiskStore: Serialises the samples to disk, rather than saving in memory
#mode=DiskStore
# Same as DiskStore but strips response data from SampleResult
#mode=StrippedDiskStore
# Note: the mode is currently resolved on the client;
# other properties (e.g. time_threshold) are resolved on the server.

#---------------------------------------------------------------------------
# JDBC Request configuration
#---------------------------------------------------------------------------

# String used to indicate a null value
#jdbcsampler.nullmarker=]NULL[
#
# Max size of BLOBs and CLOBs to store in JDBC sampler. Result will be cut off
#jdbcsampler.max_retain_result_size=65536

# Database validation query
# based in https://stackoverflow.com/questions/10684244/dbcp-validationquery-for-different-databases list
jdbc.config.check.query=select 1 from INFORMATION_SCHEMA.SYSTEM_USERS|select 1 from dual|select 1 from sysibm.sysdummy1|select 1|select 1 from rdb$database
jdbc.config.jdbc.driver.class=com.mysql.jdbc.Driver|org.postgresql.Driver|oracle.jdbc.OracleDriver|com.ingres.jdbc.IngresDriver|com.microsoft.sqlserver.jdbc.SQLServerDriver|com.microsoft.jdbc.sqlserver.SQLServerDriver|org.apache.derby.jdbc.ClientDriver|org.hsqldb.jdbc.JDBCDriver|com.ibm.db2.jcc.DB2Driver|org.apache.derby.jdbc.ClientDriver|org.h2.Driver|org.firebirdsql.jdbc.FBDriver|org.mariadb.jdbc.Driver|org.sqlite.JDBC|net.sourceforge.jtds.jdbc.Driver|com.exasol.jdbc.EXADriver

#---------------------------------------------------------------------------
# OS Process Sampler configuration
#---------------------------------------------------------------------------
# Polling to see if process has finished its work, used when a timeout is configured on sampler
#os_sampler.poll_for_timeout=100

#---------------------------------------------------------------------------
# TCP Sampler configuration
#---------------------------------------------------------------------------

# The default handler class
#tcp.handler=TCPClientImpl
#
# eolByte = byte value for end of line
# set this to a value outside the range -128 to +127 to skip eol checking
#tcp.eolByte=1000
#
# TCP Charset, used by org.apache.jmeter.protocol.tcp.sampler.TCPClientImpl
# default to Platform defaults charset as returned by Charset.defaultCharset().name()
#tcp.charset=
#
# status.prefix and suffix = strings that enclose the status response code
#tcp.status.prefix=Status=
#tcp.status.suffix=.
#
# status.properties = property file to convert codes to messages
#tcp.status.properties=mytestfiles/tcpstatus.properties

# The length prefix used by LengthPrefixedBinaryTCPClientImpl implementation
# defaults to 2 bytes.
#tcp.binarylength.prefix.length=2

#---------------------------------------------------------------------------
# Summariser - Generate Summary Results - configuration (mainly applies to non-GUI mode)
#---------------------------------------------------------------------------
#
# Comment the following property to disable the default non-GUI summariser
# [or change the value to rename it]
# (applies to non-GUI mode only)
summariser.name=summary
#
# interval between summaries (in seconds) default 30 seconds
#summariser.interval=30
#
# Write messages to log file
#summariser.log=true
#
# Write messages to System.out
#summariser.out=true

# Ignore SampleResults generated by TransactionControllers
# defaults to true
#summariser.ignore_transaction_controller_sample_result=true


#---------------------------------------------------------------------------
# Aggregate Report and Aggregate Graph - configuration
#---------------------------------------------------------------------------
#
# Percentiles to display in reports
# Can be float value between 0 and 100
# First percentile to display, defaults to 90%
#aggregate_rpt_pct1=90
# Second percentile to display, defaults to 95%
#aggregate_rpt_pct2=95
# Second percentile to display, defaults to 99%
#aggregate_rpt_pct3=99

#---------------------------------------------------------------------------
# BackendListener - configuration
#---------------------------------------------------------------------------
#
# Backend metrics window mode (fixed=fixed-size window, timed=time boxed)
#backend_metrics_window_mode=fixed
# Backend metrics sliding window size for Percentiles, Min, Max
#backend_metrics_window=100

# Backend metrics sliding window size for Percentiles, Min, Max
# when backend_metrics_window_mode is timed
# Setting this value too high can lead to OOM
#backend_metrics_large_window=5000

########################
# Graphite Backend
########################
# Send interval in second
# Defaults to 1 second
#backend_graphite.send_interval=1

########################
# Influx Backend
########################

# Send interval in second
# Defaults to 5 seconds
#backend_influxdb.send_interval=5
#Influxdb timeouts
#backend_influxdb.connection_timeout=1000
#backend_influxdb.socket_timeout=3000
#backend_influxdb.connection_request_timeout=100

#---------------------------------------------------------------------------
# BeanShell configuration
#---------------------------------------------------------------------------

# BeanShell Server properties
#
# Define the port number as non-zero to start the http server on that port
#beanshell.server.port=9000
# The telnet server will be started on the next port

#
# Define the server initialisation file
beanshell.server.file=../extras/startup.bsh

#
# Define a file to be processed at startup
# This is processed using its own interpreter.
#beanshell.init.file=

#
# Define the initialisation files for BeanShell Sampler, Function and other BeanShell elements
# N.B. Beanshell test elements do not share interpreters.
#      Each element in each thread has its own interpreter.
#      This is retained between samples.
#beanshell.sampler.init=BeanShellSampler.bshrc
#beanshell.function.init=BeanShellFunction.bshrc
#beanshell.assertion.init=BeanShellAssertion.bshrc
#beanshell.listener.init=etc
#beanshell.postprocessor.init=etc
#beanshell.preprocessor.init=etc
#beanshell.timer.init=etc

# The file BeanShellListeners.bshrc contains sample definitions
# of Test and Thread Listeners.

#---------------------------------------------------------------------------
# JSR-223 function
#---------------------------------------------------------------------------

# Path to JSR-223 file containing script to call on JMeter startup
# JMeter will try to guess the engine to use by the extension
# of the name of the file.
# This script can use pre-defined variables:
# log : Logger to log any message
# props : JMeter Property
# OUT : System.OUT
#jsr223.init.file=

#---------------------------------------------------------------------------
# Groovy function
#---------------------------------------------------------------------------

#Path to Groovy file containing utility functions to make available to __groovy function
#groovy.utilities=

# Example
#groovy.utilities=bin/utility.groovy

#---------------------------------------------------------------------------
# MailerModel configuration
#---------------------------------------------------------------------------

# Number of successful samples before a message is sent
#mailer.successlimit=2
#
# Number of failed samples before a message is sent
#mailer.failurelimit=2

#---------------------------------------------------------------------------
# CSVRead configuration
#---------------------------------------------------------------------------

# CSVRead delimiter setting (default ",")
# Make sure that there are no trailing spaces or tabs after the delimiter
# characters, or these will be included in the list of valid delimiters
#csvread.delimiter=,
#csvread.delimiter=;
#csvread.delimiter=!
#csvread.delimiter=~
# The following line has a tab after the =
#csvread.delimiter=

#---------------------------------------------------------------------------
# __time() function configuration
#
# The properties below can be used to redefine the default formats
#---------------------------------------------------------------------------
#time.YMD=yyyyMMdd
#time.HMS=HHmmss
#time.YMDHMS=yyyyMMdd-HHmmss
#time.USER1=
#time.USER2=

#---------------------------------------------------------------------------
# CSV DataSet configuration
#---------------------------------------------------------------------------

# String to return at EOF (if recycle not used)
#csvdataset.eofstring=<EOF>
#list in https://docs.oracle.com/javase/8/docs/technotes/guides/intl/encoding.doc.html
csvdataset.file.encoding_list=UTF-8|UTF-16|ISO-8859-15|US-ASCII


#---------------------------------------------------------------------------
# LDAP Sampler configuration
#---------------------------------------------------------------------------
# Maximum number of search results returned by a search that will be sorted
# to guarantee a stable ordering (if more results then this limit are returned
# then no sorting is done). Set to 0 to turn off all sorting, in which case
# "Equals" response assertions will be very likely to fail against search results.
#
#ldapsampler.max_sorted_results=1000

# Number of characters to log for each of three sections (starting matching section, diff section,
#   ending matching section where not all sections will appear for all diffs) diff display when an Equals
#   assertion fails. So a value of 100 means a maximum of 300 characters of diff text will be displayed
#   (+ a number of extra characters like "..." and "[[["/"]]]" which are used to decorate it).
#assertion.equals_section_diff_len=100
# test written out to log to signify start/end of diff delta
#assertion.equals_diff_delta_start=[[[
#assertion.equals_diff_delta_end=]]]

#---------------------------------------------------------------------------
# Miscellaneous configuration
#---------------------------------------------------------------------------

# Size of cache used by CSS Selector Extractor (for JODD implementation only)
# to store parsed CSS Selector expressions.
#cssselector.parser.cache.size=400


# Used to control what happens when you start a test and
# have listeners that could overwrite existing result files
# Possible values:
# ASK : Ask user
# APPEND : Append results to existing file
# DELETE : Delete existing file and start a new file
#resultcollector.action_if_file_exists=ASK

# If defined, then start the mirror server on the port
#mirror.server.port=8081

# ORO PatternCacheLRU size
#oro.patterncache.size=1000

#TestBeanGui
#
#propertyEditorSearchPath=null

# Turn expert mode on/off: expert mode will show expert-mode beans and properties
#jmeter.expertMode=true

# Max size of bytes stored in memory per SampleResult
# Ensure you don't exceed max capacity of a Java Array and remember
# that the higher it is, the higher JMeter will consume heap
# Defaults to 0, which means no truncation
#httpsampler.max_bytes_to_store_per_request=0

# Max size of buffer in bytes used when reading responses
# Defaults to 64k
#httpsampler.max_buffer_size=66560

# Maximum redirects to follow in a single sequence (default 20)
#httpsampler.max_redirects=20
# Maximum frame/iframe nesting depth (default 5)
#httpsampler.max_frame_depth=5

# Revert to BUG 51939 behaviour (no separate container for embedded resources) by setting the following false:
#httpsampler.separate.container=true

# If embedded resources download fails due to missing resources or other reasons, if this property is true
# Parent sample will not be marked as failed
#httpsampler.ignore_failed_embedded_resources=false

#keep alive time for the parallel download threads (in seconds)
#httpsampler.parallel_download_thread_keepalive_inseconds=60

# Don't keep the embedded resources response data : just keep the size and the md5
# default to false
#httpsampler.embedded_resources_use_md5=false

# List of extra HTTP methods that should be available in select box
#httpsampler.user_defined_methods=VERSION-CONTROL,REPORT,CHECKOUT,CHECKIN,UNCHECKOUT,MKWORKSPACE,UPDATE,LABEL,MERGE,BASELINE-CONTROL,MKACTIVITY

# The encoding to be used if none is provided (default ISO-8859-1)
sampleresult.default.encoding=UTF-8

# CookieManager behaviour - should cookies with null/empty values be deleted?
# Default is true. Use false to revert to original behaviour
#CookieManager.delete_null_cookies=true

# CookieManager behaviour - should variable cookies be allowed?
# Default is true. Use false to revert to original behaviour
#CookieManager.allow_variable_cookies=true

# CookieManager behaviour - should Cookies be stored as variables?
# Default is false
#CookieManager.save.cookies=false

# CookieManager behaviour - prefix to add to cookie name before storing it as a variable
# Default is COOKIE_; to remove the prefix, define it as one or more spaces
#CookieManager.name.prefix=

# CookieManager behaviour - check received cookies are valid before storing them?
# Default is true. Use false to revert to previous behaviour
#CookieManager.check.cookies=true

# Netscape HTTP Cookie file
cookies=cookies

# Ability to switch to Nashorn as default Javascript Engine used by MsIfController and __javaScript function
# JMeter works as following:
# - JDK >= 8 and javascript.use_rhino=false or not set : Nashorn
# - JDK >= 8 and javascript.use_rhino=true: Rhino
# If you want to use Rhino on JDK8, set this property to true
#javascript.use_rhino=false

# Number of milliseconds to wait for a thread to stop
#jmeterengine.threadstop.wait=5000

#Whether to invoke System.exit(0) in server exit code after stopping RMI
#jmeterengine.remote.system.exit=false

# Whether to call System.exit(1) on failure to stop threads in non-GUI mode.
# This only takes effect if the test was explicitly requested to stop.
# If this is disabled, it may be necessary to kill the JVM externally
#jmeterengine.stopfail.system.exit=true

# Whether to force call System.exit(0) at end of test in non-GUI mode, even if
# there were no failures and the test was not explicitly asked to stop.
# Without this, the JVM may never exit if there are other threads spawned by
# the test which never exit.
#jmeterengine.force.system.exit=false

# How long to pause (in ms) in the daemon thread before reporting that the JVM has failed to exit.
# If the value is <= 0, the JMeter does not start the daemon thread
#jmeter.exit.check.pause=2000

# If running non-GUI, then JMeter listens on the following port for a shutdown message.
# To disable, set the port to 1000 or less.
#jmeterengine.nongui.port=4445
#
# If the initial port is busy, keep trying until this port is reached
# (to disable searching, set the value less than or equal to the .port property)
#jmeterengine.nongui.maxport=4455

# How often to check for shutdown during ramp-up (milliseconds)
#jmeterthread.rampup.granularity=1000

#Should JMeter expand the tree when loading a test plan?
# default value is false since JMeter 2.7
#onload.expandtree=false

#JSyntaxTextArea configuration
#jsyntaxtextarea.wrapstyleword=true
#jsyntaxtextarea.linewrap=true
#jsyntaxtextarea.codefolding=true
# Set 0 to disable undo feature in JSyntaxTextArea
#jsyntaxtextarea.maxundos=50
# Change the font on the (JSyntax) Text Areas. (Useful for HiDPI screens)
#jsyntaxtextarea.font.family=Hack
#jsyntaxtextarea.font.size=14

# Set this to false to disable the use of JSyntaxTextArea for the Console Logger panel
#loggerpanel.usejsyntaxtext=true

# Maximum size of HTML page that can be displayed; default=10 mbytes
# Set to 0 to disable the size check and display the whole response
#view.results.tree.max_size=10485760

# Order of Renderers in View Results Tree
# Note full class names should be used for non jmeter core renderers
# For JMeter core renderers, class names start with . and are automatically
# prefixed with org.apache.jmeter.visualizers
view.results.tree.renderers_order=.RenderAsText,.RenderAsRegexp,.RenderAsBoundaryExtractor,.RenderAsCssJQuery,.RenderAsXPath,org.apache.jmeter.extractor.json.render.RenderAsJsonRenderer,.RenderAsHTML,.RenderAsHTMLFormatted,.RenderAsHTMLWithEmbedded,.RenderAsDocument,.RenderAsJSON,.RenderAsXML

# Maximum number of results in the results tree
# Set to 0 to store all results (might consume a lot of memory)
#view.results.tree.max_results=500

# Maximum size of Document that can be parsed by Tika engine; default=10 * 1024 * 1024 (10MB)
# Set to 0 to disable the size check
#document.max_size=0

#JMS options
# Enable the following property to stop JMS Point-to-Point Sampler from using
# the properties java.naming.security.[principal|credentials] when creating the queue connection
#JMSSampler.useSecurity.properties=false

# Set the following value to true in order to skip the delete confirmation dialogue
#confirm.delete.skip=false

# Used by JSR223 elements
# Size of compiled scripts cache
#jsr223.compiled_scripts_cache_size=100

#---------------------------------------------------------------------------
# Classpath configuration
#---------------------------------------------------------------------------

# List of directories (separated by ;) to search for additional JMeter plugin classes,
# for example new GUI elements and samplers.
# Any jar file in such a directory will be automatically included,
# jar files in sub directories are ignored.
# The given value is in addition to any jars found in the lib/ext directory.
# Do not use this for utility or plugin dependency jars.
#search_paths=/app1/lib;/app2/lib

# List of directories that JMeter will search for utility and plugin dependency classes.
# Use your platform path separator to separate multiple paths.
# Any jar file in such a directory will be automatically included,
# jar files in sub directories are ignored.
# The given value is in addition to any jars found in the lib directory.
# All entries will be added to the class path of the system class loader
# and also to the path of the JMeter internal loader.
# Paths with spaces may cause problems for the JVM
#user.classpath=../classes;../lib

# List of directories (separated by ;) that JMeter will search for utility
# and plugin dependency classes.
# Any jar file in such a directory will be automatically included,
# jar files in sub directories are ignored.
# The given value is in addition to any jars found in the lib directory
# or given by the user.classpath property.
# All entries will be added to the path of the JMeter internal loader only.
# For plugin dependencies this property should be used instead of user.classpath.
#plugin_dependency_paths=../dependencies/lib;../app1/;../app2/

# Classpath finder
# ================
# The classpath finder currently needs to load every single JMeter class to find
# the classes it needs.
# For non-GUI mode, it's only necessary to scan for Function classes, but all classes
# are still loaded.
# All current Function classes include ".function." in their name,
# and none include ".gui." in the name, so the number of unwanted classes loaded can be
# reduced by checking for these. However, if a valid function class name does not match
# these restrictions, it will not be loaded. If problems are encountered, then comment
# or change the following properties:
classfinder.functions.contain=.functions.
classfinder.functions.notContain=.gui.


#---------------------------------------------------------------------------
# Additional property files to load
#---------------------------------------------------------------------------

# Should JMeter automatically load additional JMeter properties?
# File name to look for (comment to disable)
user.properties=user.properties

# Should JMeter automatically load additional system properties?
# File name to look for (comment to disable)
system.properties=system.properties

# Comma separated list of files that contain reference to templates and their description
# Path must be relative to JMeter root folder
#template.files=/bin/templates/templates.xml


#---------------------------------------------------------------------------
# Thread Group Validation feature
#---------------------------------------------------------------------------

# Validation is the name of the feature used to rapidly validate a Thread Group runs fine
# Default implementation is org.apache.jmeter.gui.action.validation.TreeClonerForValidation
# It runs validation without timers, with 1 thread, 1 iteration and Startup Delay set to 0
# You can implement your own policy that must extend org.apache.jmeter.engine.TreeCloner
# JMeter will instantiate it and use it to create the Tree used to run validation on Thread Group
#testplan_validation.tree_cloner_class=org.apache.jmeter.validation.ComponentTreeClonerForValidation

# Number of threads to use to validate a Thread Group
#testplan_validation.nb_threads_per_thread_group=1

# Ignore BackendListener when validating the thread group of plan
#testplan_validation.ignore_backends=true

# Ignore timers when validating the thread group of plan
#testplan_validation.ignore_timers=true

# Number of iterations to use to validate a Thread Group
#testplan_validation.number_iterations=1

# Force throuput controllers that work in percentage mode to be a 100%
# Disabled by default
#testplan_validation.tpc_force_100_pct=false

#---------------------------------------------------------------------------
# Think Time configuration
#---------------------------------------------------------------------------

#
# Apply a factor on computed pauses by the following Timers:
# - Gaussian Random Timer
# - Uniform Random Timer
# - Poisson Random Timer
#
#timer.factor=1.0f

# Default implementation that create the Timer structure to add to Test Plan
# Implementation of interface org.apache.jmeter.gui.action.thinktime.ThinkTimeCreator
#think_time_creator.impl=org.apache.jmeter.thinktime.DefaultThinkTimeCreator

# Default Timer GUI class added to Test Plan by DefaultThinkTimeCreator
#think_time_creator.default_timer_implementation=org.apache.jmeter.timers.gui.UniformRandomTimerGui

# Default constant pause of Timer
#think_time_creator.default_constant_pause=1000

# Default range pause of Timer
#think_time_creator.default_range=100


# Change this parameter if you want to override the APDEX satisfaction threshold.
jmeter.reportgenerator.apdex_satisfied_threshold=500

# Change this parameter if you want to override the APDEX tolerance threshold.
jmeter.reportgenerator.apdex_tolerated_threshold=1500

# Timeout in milliseconds for Report generation when using Tools > Generate HTML report
#generate_report_ui.generation_timeout=120000
#---------------------------------------------------------------------------
# Naming Policy configuration
#---------------------------------------------------------------------------

# Prefix used when naming elements
#naming_policy.prefix=
# Suffix used when naming elements
#naming_policy.suffix=

# Implementation of interface org.apache.jmeter.gui.action.TreeNodeNamingPolicy
#naming_policy.impl=org.apache.jmeter.gui.action.impl.DefaultTreeNodeNamingPolicy

#---------------------------------------------------------------------------
# Help Documentation
#---------------------------------------------------------------------------

# Switch that allows using Local documentation opened in JMeter GUI
# By default we use Online documentation opened in Browser
#help.local=false

#---------------------------------------------------------------------------
# Documentation generation
#---------------------------------------------------------------------------

# Path to XSL file used to generate Schematic View of Test Plan
# When empty, JMeter will use the embedded one in src/core/org/apache/jmeter/gui/action/schematic.xsl
#docgeneration.schematic_xsl=
