package com.kf.aitest.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 单阶段数据DTO
 */
@Data
public class StageDataDTO {
    
    /**
     * 阶段名称：recognize、extraction、structured、transformer
     */
    private String stageName;

    /**
     * 请求文件的MD5哈希值
     */
    private String fileMd5;

    /**
     * 数据类型：json、md等
     */
    private String dataType;
    
    /**
     * UAT环境数据
     */
    private Object uatData;
    
    /**
     * TEST环境数据
     */
    private Object testData;
    
    /**
     * 数据获取状态：SUCCESS、FAILED、TIMEOUT
     */
    private String status;
    
    /**
     * 错误信息（如果获取失败）
     */
    private String errorMessage;
    
    /**
     * 数据获取耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 获取时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fetchTime;

    /**
     * AI评估结果
     */
    private String aiEvaluation;

    /**
     * AI评估得分（0-100）
     */
    private Integer aiScore;
}
