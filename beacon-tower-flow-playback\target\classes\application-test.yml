server:
  port: 8001
  tomcat:
    max-swallow-size: -1
kf:
  git:
    local:
      directory: D:\work\branch1
  maven:
    settings: ./src/main/resources/static/settings.xml
  pa:
#    interface: http://localhost:8080/interface/getAnnotation?taskId=
    interface: http://localhost:3002/seigneur/InterfaceDetailsList/
spring:
  application:
    name: beacon-tower-accurate-test
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  datasource:
    #   数据源基本配置
    username: root
    password: zc1021
    driver-class-name: org.mariadb.jdbc.Driver
    url: jdbc:mariadb://*************:3306/k_base?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      validation-query: SELECT 1
  rabbitmq:
    queue: kf_analyze_queue
    listener:
      simple:
        retry:
          max-attempts: 1
    username: fs
    password: FS_adminuser@123
    host: test-mq-001.taimei.com
    port: 5672
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml


