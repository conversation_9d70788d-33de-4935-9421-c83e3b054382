package com.kf.aitest.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 独立任务列表DTO
 * 用于列表查询响应，是IndependentTaskDTO的简化版本
 * 只包含列表展示所需的关键字段，不包含详细的UAT/TEST数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IndependentTaskListDTO {
    
    /**
     * 独立任务ID（UUID）
     */
    private String taskId;
    
    /**
     * 文件MD5哈希值
     */
    private String fileMd5;
    
    /**
     * 阶段名称：recognize、extraction、structured、transformer
     */
    private String stage;
    
    /**
     * 任务状态：PENDING、RUNNING、SUCCESS、FAILED、TIMEOUT
     */
    private String status;
    
    /**
     * AI评估得分(0-100)
     */
    private Integer aiScore;
    
    /**
     * 任务执行耗时(毫秒)
     */
    private Long duration;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 错误信息（如果任务失败）
     */
    private String errorMessage;
    
    /**
     * 数据类型：json、md等
     */
    private String dataType;
    
    /**
     * 判断任务是否成功完成
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }
    
    /**
     * 判断任务是否失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status) || "TIMEOUT".equals(status);
    }
    
    /**
     * 判断任务是否正在运行
     */
    public boolean isRunning() {
        return "RUNNING".equals(status);
    }
    
    /**
     * 判断任务是否待执行
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }
    
    /**
     * 获取状态显示文本
     */
    public String getStatusText() {
        switch (status != null ? status : "PENDING") {
            case "PENDING":
                return "等待执行";
            case "RUNNING":
                return "执行中";
            case "SUCCESS":
                return "执行成功";
            case "FAILED":
                return "执行失败";
            case "TIMEOUT":
                return "执行超时";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 获取执行耗时的友好显示
     */
    public String getDurationText() {
        if (duration == null || duration <= 0) {
            return "-";
        }
        
        if (duration < 1000) {
            return duration + "ms";
        } else if (duration < 60000) {
            return String.format("%.1fs", duration / 1000.0);
        } else {
            long minutes = duration / 60000;
            long seconds = (duration % 60000) / 1000;
            return String.format("%dm%ds", minutes, seconds);
        }
    }
    
    /**
     * 获取AI评分的显示文本
     */
    public String getAiScoreText() {
        if (aiScore == null) {
            return "-";
        }
        return aiScore + "分";
    }
}
