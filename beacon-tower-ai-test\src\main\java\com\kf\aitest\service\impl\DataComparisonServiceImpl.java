package com.kf.aitest.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kf.aitest.dto.DataComparisonRequestDTO;
import com.kf.aitest.dto.StageDataDTO;
import com.kf.aitest.dto.IndependentTaskDTO;
import com.kf.aitest.dto.IndependentTaskListDTO;
import com.kf.aitest.service.AiEvaluationService;
import com.kf.aitest.service.ComparisonProgressManager;
import com.kf.aitest.entity.TDataComparisonStage;
import com.kf.aitest.service.DataComparisonService;
import com.kf.aitest.service.DataFetchService;
import com.kf.aitest.service.ResultPrintService;
import com.kf.aitest.dao.TDataComparisonStageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.nio.charset.StandardCharsets;

/**
 * 数据对比服务实现
 */
@Slf4j
@Service
public class DataComparisonServiceImpl implements DataComparisonService {
    
    @Autowired
    private DataFetchService dataFetchService;

    @Autowired
    private AiEvaluationService aiEvaluationService;

    @Autowired
    private ComparisonProgressManager progressManager;

    @Autowired
    private ResultPrintService resultPrintService;

    @Autowired
    private TDataComparisonStageMapper dataComparisonStageMapper;

    @Value("${data.comparison.uat.url:https://copilot-uat.pharmaronclinical.com}")
    private String defaultUatUrl;
    
    @Value("${data.comparison.test.url:https://copilot-test.pharmaronclinical.com}")
    private String defaultTestUrl;
    
    // 数据处理的四个阶段，按顺序执行
    private static final List<String> DEFAULT_STAGES = Arrays.asList("recognize", "extraction", "structured", "transformer");
    
    @Override
    public SseEmitter createSseConnection(String taskId) {
        return progressManager.createEmitter(taskId);
    }
    
    /**
     * 启动独立任务处理
     * 将请求拆分为多个完全独立的任务，每个任务处理一个(id, stage)组合
     */
    public List<IndependentTaskDTO> startIndependentTasks(DataComparisonRequestDTO request) {
        log.info("开始创建独立任务: ids={}, stages={}", request.getIds(), request.getStages());

        // 计算请求数据的MD5哈希值
        String fileMd5 = calculateRequestMd5(request);
        log.info("计算请求MD5: fileMd5={}", fileMd5);

        List<IndependentTaskDTO> tasks = new ArrayList<>();

        try {
            // 确定要处理的阶段列表
            List<String> stagesToProcess = request.getStages() != null && !request.getStages().isEmpty()
                    ? request.getStages()
                    : DEFAULT_STAGES;

            // 获取环境URL
            String uatUrl = request.getUatBaseUrl() != null ? request.getUatBaseUrl() : getDefaultUatUrl();
            String testUrl = request.getTestBaseUrl() != null ? request.getTestBaseUrl() : getDefaultTestUrl();

            // 为每个(id, stage)组合创建独立任务
            for (String id : request.getIds()) {
                for (String stage : stagesToProcess) {
                    String taskId = java.util.UUID.randomUUID().toString();

                    // 创建独立任务DTO
                    IndependentTaskDTO task = new IndependentTaskDTO();
                    task.setTaskId(taskId);
                    task.setFileMd5(fileMd5);
                    task.setStage(stage);
                    task.setStatus("PENDING");
                    task.setCreateTime(LocalDateTime.now());

                    tasks.add(task);

                    // 立即开始处理这个独立任务
                    CompletableFuture.runAsync(() -> processIndependentTask(taskId, id, stage, request, fileMd5, uatUrl, testUrl));
                }
            }

            log.info("创建了{}个独立任务", tasks.size());

        } catch (Exception e) {
            log.error("创建独立任务失败: error={}", e.getMessage(), e);
            throw new RuntimeException("创建独立任务失败: " + e.getMessage(), e);
        }

        return tasks;
    }

    @Override
    @Async
    public void startComparison(String taskId, DataComparisonRequestDTO request) {
        // 保持向后兼容，但内部使用新的独立任务逻辑
        log.warn("使用了旧的startComparison方法，建议使用startIndependentTasks方法");
        startIndependentTasks(request);
    }
    
    /**
     * 处理单个独立任务
     *
     * @param taskId 独立任务ID
     * @param dataId 数据ID
     * @param stage 阶段名称
     * @param request 原始请求
     * @param fileMd5 文件MD5
     * @param uatUrl UAT环境URL
     * @param testUrl TEST环境URL
     */
    private void processIndependentTask(String taskId, String dataId, String stage,
                                      DataComparisonRequestDTO request, String fileMd5,
                                      String uatUrl, String testUrl) {
        log.info("开始处理独立任务: taskId={}, dataId={}, stage={}", taskId, dataId, stage);

        try {
            // 初始化任务进度
            progressManager.initProgress(taskId, 1); // 独立任务总数为1
            progressManager.updateCurrentTask(taskId, dataId, stage, 0);

            // 更新状态为RUNNING
            updateIndependentTaskStatus(taskId, "RUNNING");

            // 获取阶段数据
            StageDataDTO stageData = dataFetchService.fetchStageData(
                    dataId, stage, uatUrl, testUrl, request.getTimeoutSeconds());

            // 设置fileMd5到阶段数据中
            stageData.setFileMd5(fileMd5);

            // 保存独立任务数据到数据库（如果有用户ID）
            if (request.getUserId() != null && "SUCCESS".equals(stageData.getStatus())) {
                saveIndependentTaskToDatabase(taskId, dataId, stage, stageData, fileMd5);
            }

            // 进行AI评估（如果启用且数据获取成功）
            if (request.getEnableAiEvaluation() && "SUCCESS".equals(stageData.getStatus())) {
                stageData = performIndependentTaskAiEvaluation(taskId, stageData);
            }

            // 更新状态为SUCCESS
            updateIndependentTaskStatus(taskId, "SUCCESS");

            // 完成任务
            progressManager.completeTask(taskId);

            log.info("独立任务处理完成: taskId={}, dataId={}, stage={}, status={}",
                    taskId, dataId, stage, stageData.getStatus());

        } catch (Exception e) {
            log.error("独立任务处理失败: taskId={}, dataId={}, stage={}, error={}",
                    taskId, dataId, stage, e.getMessage(), e);

            // 更新状态为FAILED
            updateIndependentTaskStatus(taskId, "FAILED", e.getMessage());

            // 发送错误信息
            progressManager.sendError(taskId,
                    String.format("任务失败: %s-%s, %s", dataId, stage, e.getMessage()));
        }
    }

    // 注意：原processId方法已被processTask方法替代，实现真正的单任务并发处理

    // 注意：processTask方法已被删除，使用processIndependentTask方法替代

    // 注意：saveStageDataToDatabase方法已在processIndependentTask中重新实现

    // 注意：performAiEvaluation方法已在processIndependentTask中重新实现

    /**
     * 更新AI评估结果到数据库
     *
     * @param taskId 任务ID
     * @param stageData 阶段数据
     */
    private void updateAiEvaluationResult(String taskId, StageDataDTO stageData) {
        try {
            TDataComparisonStage updateStage = new TDataComparisonStage();
            updateStage.setTaskId(taskId);
            updateStage.setAiEvaluation(stageData.getAiEvaluation());
            updateStage.setAiScore(stageData.getAiScore());
            dataComparisonStageMapper.updateById(updateStage);

            log.debug("AI评估结果已更新到数据库: taskId={}, score={}",
                    taskId, stageData.getAiScore());
        } catch (Exception e) {
            log.error("更新AI评估结果失败: taskId={}, error={}", taskId, e.getMessage());
        }
    }

    @Override
    public String getDefaultUatUrl() {
        return defaultUatUrl;
    }

    @Override
    public String getDefaultTestUrl() {
        return defaultTestUrl;
    }



    /**
     * 计算请求数据的MD5哈希值
     *
     * @param request 请求对象
     * @return MD5哈希值
     */
    private String calculateRequestMd5(DataComparisonRequestDTO request) {
        try {
            // 构建用于计算MD5的字符串
            StringBuilder sb = new StringBuilder();

            // 添加关键请求参数
            if (request.getIds() != null) {
                sb.append("ids:").append(String.join(",", request.getIds()));
            }
            if (request.getUatBaseUrl() != null) {
                sb.append("|uatUrl:").append(request.getUatBaseUrl());
            }
            if (request.getTestBaseUrl() != null) {
                sb.append("|testUrl:").append(request.getTestBaseUrl());
            }
            if (request.getStages() != null) {
                sb.append("|stages:").append(String.join(",", request.getStages()));
            }
            sb.append("|enableAi:").append(request.getEnableAiEvaluation());
            sb.append("|disableChunking:").append(request.getDisableChunking());

            // 计算MD5
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(sb.toString().getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            log.error("计算MD5失败: {}", e.getMessage(), e);
            // 如果MD5计算失败，使用时间戳作为备选方案
            return "fallback_" + System.currentTimeMillis();
        }
    }

    // 注意：getTaskList方法已被删除，使用getIndependentTaskList方法替代

    // 注意：flattenTasks方法已被删除，独立任务模式下不需要扁平化

    @Override
    public IndependentTaskDTO getTaskDetail(String taskId) {
        log.info("查询独立任务详情: taskId={}", taskId);

        try {
            TDataComparisonStage entity = dataComparisonStageMapper.selectById(taskId);
            if (entity == null) {
                log.warn("任务不存在: taskId={}", taskId);
                return null;
            }

            // 转换为IndependentTaskDTO
            IndependentTaskDTO task = new IndependentTaskDTO();
            task.setTaskId(entity.getTaskId());
            task.setFileMd5(entity.getFileMd5());
            task.setStage(entity.getStageName());
            task.setStatus(entity.getStageStatus());
            task.setErrorMessage(entity.getErrorMessage());
            task.setAiEvaluation(entity.getAiEvaluation());
            task.setAiScore(entity.getAiScore());
            task.setDuration(entity.getDuration());
            task.setFetchTime(entity.getFetchTime());
            task.setUatData(entity.getUatData());
            task.setTestData(entity.getTestData());
            task.setDataType(entity.getDataType());
            task.setCreateTime(entity.getCreateTime());
            task.setUpdateTime(entity.getUpdateTime());

            log.info("查询独立任务详情成功: taskId={}, status={}", taskId, task.getStatus());
            return task;

        } catch (Exception e) {
            log.error("查询独立任务详情失败: taskId={}, error={}", taskId, e.getMessage(), e);
            throw new RuntimeException("查询任务详情失败: " + e.getMessage(), e);
        }
    }

    @Override
    public IPage<IndependentTaskListDTO> getIndependentTaskList(String fileMd5,
            LocalDateTime startTime, LocalDateTime endTime, Integer current, Integer size) {
        log.info("查询独立任务列表: fileMd5={}, startTime={}, endTime={}, current={}, size={}",
                fileMd5, startTime, endTime, current, size);

        try {
            // 参数验证
            if (current == null || current < 1) {
                current = 1;
            }
            if (size == null || size < 1 || size > 100) {
                size = 10;
            }

            // 创建分页对象
            Page<IndependentTaskListDTO> page = new Page<>(current, size);

            // 调用Mapper查询
            IPage<IndependentTaskListDTO> result = dataComparisonStageMapper.selectIndependentTaskList(
                    fileMd5, startTime, endTime, page);

            log.info("查询独立任务列表完成: current={}, size={}, total={}",
                    current, size, result.getTotal());
            return result;

        } catch (Exception e) {
            log.error("查询独立任务列表失败: error={}", e.getMessage(), e);
            throw new RuntimeException("查询任务列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新独立任务状态
     */
    private void updateIndependentTaskStatus(String taskId, String status) {
        updateIndependentTaskStatus(taskId, status, null);
    }

    /**
     * 更新独立任务状态
     */
    private void updateIndependentTaskStatus(String taskId, String status, String errorMessage) {
        try {
            TDataComparisonStage task = new TDataComparisonStage();
            task.setTaskId(taskId);
            task.setStageStatus(status);
            if (errorMessage != null) {
                task.setErrorMessage(errorMessage);
            }
            task.setUpdateTime(LocalDateTime.now());
            dataComparisonStageMapper.updateById(task);

            log.debug("独立任务状态已更新: taskId={}, status={}", taskId, status);
        } catch (Exception e) {
            log.error("更新独立任务状态失败: taskId={}, error={}", taskId, e.getMessage());
        }
    }

    /**
     * 保存独立任务到数据库
     */
    private void saveIndependentTaskToDatabase(String taskId, String dataId, String stage,
                                             StageDataDTO stageData, String fileMd5) {
        try {
            TDataComparisonStage task = new TDataComparisonStage();
            task.setTaskId(taskId);
            task.setFileMd5(fileMd5);
            task.setStageName(stage);
            task.setDataType(stageData.getDataType());
            task.setStageStatus(stageData.getStatus());
            task.setErrorMessage(stageData.getErrorMessage());
            task.setDuration(stageData.getDuration());
            task.setFetchTime(stageData.getFetchTime());

            // 将Object类型转换为String类型
            if (stageData.getUatData() != null) {
                task.setUatData(stageData.getUatData().toString());
            }
            if (stageData.getTestData() != null) {
                task.setTestData(stageData.getTestData().toString());
            }

            dataComparisonStageMapper.insert(task);

            log.info("独立任务数据已保存到数据库: taskId={}, stage={}", taskId, stage);
        } catch (Exception e) {
            log.error("保存独立任务数据失败: taskId={}, stage={}, error={}", taskId, stage, e.getMessage(), e);
        }
    }

    /**
     * 执行独立任务的AI评估
     */
    private StageDataDTO performIndependentTaskAiEvaluation(String taskId, StageDataDTO stageData) {
        try {
            // 检查是否为AiEvaluationServiceImpl实例，支持禁用分片
            if (aiEvaluationService instanceof com.kf.aitest.service.impl.AiEvaluationServiceImpl) {
                com.kf.aitest.service.impl.AiEvaluationServiceImpl impl =
                    (com.kf.aitest.service.impl.AiEvaluationServiceImpl) aiEvaluationService;
                stageData = impl.evaluateStageData(taskId, stageData, false); // 默认不禁用分片
            } else {
                stageData = aiEvaluationService.evaluateStageData(taskId, stageData);
            }

            // 更新数据库中的AI评估结果
            updateAiEvaluationResult(taskId, stageData);

            return stageData;
        } catch (Exception e) {
            log.error("AI评估失败: taskId={}, error={}", taskId, e.getMessage(), e);
            stageData.setErrorMessage("AI评估失败: " + e.getMessage());
            return stageData;
        }
    }

}
