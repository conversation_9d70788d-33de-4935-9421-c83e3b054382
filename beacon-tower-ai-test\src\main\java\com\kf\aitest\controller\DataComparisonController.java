package com.kf.aitest.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kf.aitest.common.PaginatedResponse;
import com.kf.aitest.common.ResponseDoMain;
import com.kf.aitest.dto.DataComparisonRequestDTO;
import com.kf.aitest.dto.IndependentTaskDTO;
import com.kf.aitest.dto.IndependentTaskListDTO;
import com.kf.aitest.service.DataComparisonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.kf.aitest.common.ResponseDoMain.error;
import static com.kf.aitest.common.ResponseDoMain.success;

/**
 * 数据对比控制器
 */
@Slf4j
@RestController
@RequestMapping("/data-comparison")
public class DataComparisonController {
    
    @Autowired
    private DataComparisonService dataComparisonService;
    
    /**
     * 创建SSE连接，用于接收对比进度
     * 
     * @param taskId 任务ID
     * @return SSE连接
     */
    @GetMapping("/progress/{taskId}")
    public SseEmitter getProgress(@PathVariable String taskId) {
        log.info("创建SSE连接: taskId={}", taskId);
        return dataComparisonService.createSseConnection(taskId);
    }
    
    /**
     * 启动独立任务处理（新版本）
     * 将请求拆分为多个完全独立的任务，每个任务处理一个(id, stage)组合
     *
     * @param request 对比请求
     * @return 独立任务列表
     */
    @PostMapping("/start")
    public ResponseDoMain<Map<String, Object>> startComparison(@Valid @RequestBody DataComparisonRequestDTO request,
                                                              @RequestHeader(value = "userId", required = false) String userId) {
        // 设置用户ID（从请求头获取，如果没有则使用默认值）
        if (request.getUserId() == null) {
            request.setUserId(userId != null ? userId : "system_user");
        }

        // 确保分片设置有默认值
        if (request.getDisableChunking() == null) {
            request.setDisableChunking(false);
        }

        log.info("启动独立任务处理: ids={}, stages={}, userId={}, disableChunking={}",
                request.getIds(), request.getStages(), request.getUserId(), request.getDisableChunking());

        try {
            // 创建独立任务
            List<IndependentTaskDTO> tasks = dataComparisonService.startIndependentTasks(request);

            // 返回任务信息
            Map<String, Object> result = new HashMap<>();
            result.put("tasks", tasks);
            result.put("totalTasks", tasks.size());
            result.put("message", String.format("已创建%d个独立任务", tasks.size()));

            // 提供每个任务的进度URL
            List<String> progressUrls = tasks.stream()
                    .map(task -> "/data-comparison/progress/" + task.getTaskId())
                    .collect(java.util.stream.Collectors.toList());
            result.put("progressUrls", progressUrls);

            return success("独立任务创建成功", result);

        } catch (Exception e) {
            log.error("启动独立任务失败: error={}", e.getMessage(), e);
            return error("启动任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询单个独立任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @GetMapping("/task/{taskId}")
    public ResponseDoMain<IndependentTaskDTO> getTaskDetail(@PathVariable String taskId) {
        log.info("查询独立任务详情: taskId={}", taskId);

        try {
            IndependentTaskDTO task = dataComparisonService.getTaskDetail(taskId);
            if (task == null) {
                return error("任务不存在: " + taskId);
            }
            return success("查询成功", task);
        } catch (Exception e) {
            log.error("查询任务详情失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取环境配置信息
     * 
     * @return 环境配置
     */
    @GetMapping("/config")
    public ResponseDoMain<Map<String, String>> getConfig() {
        Map<String, String> config = new HashMap<>();
        config.put("defaultUatUrl", dataComparisonService.getDefaultUatUrl());
        config.put("defaultTestUrl", dataComparisonService.getDefaultTestUrl());
        config.put("supportedStages", "recognize,extraction,structured,transformer");
        
        return success("获取配置成功", config);
    }

    // 注意：旧版/list接口已删除，使用新版/tasks接口

    /**
     * 简化的独立任务列表查询（新版本）
     * 仅支持MD5和时间范围查询，移除所有批量处理相关的查询条件
     *
     * @param fileMd5 文件MD5（可选，支持模糊查询）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param current 当前页码
     * @param size 每页大小
     * @return 独立任务列表
     */
    @GetMapping("/tasks")
    public ResponseDoMain<IPage<IndependentTaskListDTO>> getIndependentTaskList(
            @RequestParam(required = false) String fileMd5,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {

        log.info("查询独立任务列表: fileMd5={}, startTime={}, endTime={}, current={}, size={}",
                fileMd5, startTime, endTime, current, size);

        try {
            // 解析时间参数
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;

            if (startTime != null && !startTime.trim().isEmpty()) {
                startDateTime = LocalDateTime.parse(startTime.replace(" ", "T"));
            }
            if (endTime != null && !endTime.trim().isEmpty()) {
                endDateTime = LocalDateTime.parse(endTime.replace(" ", "T"));
            }

            // 调用Service层查询
            IPage<IndependentTaskListDTO> pageResult = dataComparisonService.getIndependentTaskList(
                    fileMd5, startDateTime, endDateTime, current, size);

            return success("查询成功", pageResult);

        } catch (Exception e) {
            log.error("查询独立任务列表失败: error={}", e.getMessage(), e);
            return error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     *
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseDoMain<String> health() {
        return success("数据对比服务运行正常");
    }
}
