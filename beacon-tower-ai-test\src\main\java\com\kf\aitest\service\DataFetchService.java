package com.kf.aitest.service;

import com.kf.aitest.dto.StageDataDTO;

/**
 * 数据获取服务接口
 */
public interface DataFetchService {
    
    /**
     * 获取指定阶段的数据
     * 
     * @param id 数据ID
     * @param stageName 阶段名称：recognize、extraction、structured、transformer
     * @param uatBaseUrl UAT环境基础URL
     * @param testBaseUrl TEST环境基础URL
     * @param timeoutSeconds 超时时间（秒）
     * @return 阶段数据DTO
     */
    StageDataDTO fetchStageData(String id, String stageName, String uatBaseUrl, String testBaseUrl, Integer timeoutSeconds);
    
    /**
     * 获取阶段对应的文件名
     *
     * @param stageName 阶段名称
     * @return 文件名
     */
    String getStageFileName(String stageName);

    /**
     * 构建请求URL
     *
     * @param baseUrl 基础URL
     * @param id 数据ID
     * @param stageName 阶段名称
     * @return 构建的URL
     */
    String buildUrl(String baseUrl, String id, String stageName);
}
