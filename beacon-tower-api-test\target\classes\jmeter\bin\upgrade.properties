# Class, property and value upgrade equivalences.

##   Licensed to the Apache Software Foundation (ASF) under one or more
##   contributor license agreements.  See the NOTICE file distributed with
##   this work for additional information regarding copyright ownership.
##   The ASF licenses this file to You under the Apache License, Version 2.0
##   (the "License"); you may not use this file except in compliance with
##   the License.  You may obtain a copy of the License at
##
##       http://www.apache.org/licenses/LICENSE-2.0
##
##   Unless required by applicable law or agreed to in writing, software
##   distributed under the License is distributed on an "AS IS" BASIS,
##   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
##   See the License for the specific language governing permissions and
##   limitations under the License.

#
# Format is as follows --
# for renamed test element & GUI classes:
# 		old.class.Name=new.class.Name
# 		old.class.Name|guiClassName=new.class.Name
#		(e.g. for ConfigTestElement)
#
# for renamed / deleted properties:
#		class.Name/Old.propertyName=newPropertyName
#		if newPropertyName is omitted, then property is deleted
#
# for renamed values:
#		old.class.Name.old.propertyName/oldValue=newValue
#

org.apache.jmeter.protocol.http.config.gui.UrlConfigGui=org.apache.jmeter.protocol.http.config.gui.HttpDefaultsGui
org.apache.jmeter.assertions.Assertion=org.apache.jmeter.assertions.ResponseAssertion
org.apache.jmeter.protocol.http.sampler.HTTPSamplerFull=org.apache.jmeter.protocol.http.sampler.HTTPSampler
org.apache.jmeter.control.gui.RecordController=org.apache.jmeter.protocol.http.control.gui.RecordController

org.apache.jmeter.timers.gui.ConstantThroughputTimerGui=org.apache.jmeter.testbeans.gui.TestBeanGUI
org.apache.jmeter.timers.ConstantThroughputTimer/ConstantThroughputTimer.throughput=throughput

org.apache.jmeter.protocol.jdbc.control.gui.JdbcTestSampleGui=org.apache.jmeter.testbeans.gui.TestBeanGUI
org.apache.jmeter.protocol.jdbc.sampler.JDBCSampler/JDBCSampler.query=query
#org.apache.jmeter.protocol.jdbc.sampler.JDBCSampler.JDBCSampler.dataSource/NULL=

# Convert DBconfig
org.apache.jmeter.protocol.jdbc.config.gui.DbConfigGui=org.apache.jmeter.testbeans.gui.TestBeanGUI
org.apache.jmeter.config.ConfigTestElement|org.apache.jmeter.protocol.jdbc.config.gui.DbConfigGui=org.apache.jmeter.protocol.jdbc.config.DataSourceElement
org.apache.jmeter.protocol.jdbc.config.DataSourceElement/JDBCSampler.url=dbUrl
org.apache.jmeter.protocol.jdbc.config.DataSourceElement/JDBCSampler.driver=driver
org.apache.jmeter.protocol.jdbc.config.DataSourceElement/JDBCSampler.query=query
org.apache.jmeter.protocol.jdbc.config.DataSourceElement/ConfigTestElement.username=username
org.apache.jmeter.protocol.jdbc.config.DataSourceElement/ConfigTestElement.password=password

# Convert PoolConfig
org.apache.jmeter.protocol.jdbc.config.gui.PoolConfigGui=org.apache.jmeter.testbeans.gui.TestBeanGUI
org.apache.jmeter.config.ConfigTestElement|org.apache.jmeter.protocol.jdbc.config.gui.PoolConfigGui=org.apache.jmeter.protocol.jdbc.config.DataSourceElement
org.apache.jmeter.protocol.jdbc.config.DataSourceElement/JDBCSampler.connections=
org.apache.jmeter.protocol.jdbc.config.DataSourceElement/JDBCSampler.connPoolClass=
org.apache.jmeter.protocol.jdbc.config.DataSourceElement/JDBCSampler.maxuse=poolMax

# SQL Config
org.apache.jmeter.config.ConfigTestElement/JDBCSampler.query=query
org.apache.jmeter.protocol.http.control.Header/TestElement.name=Header.name

# Upgrade AccessLogSampler
org.apache.jmeter.protocol.http.control.gui.AccessLogSamplerGui=org.apache.jmeter.testbeans.gui.TestBeanGUI
org.apache.jmeter.protocol.http.sampler.AccessLogSampler/AccessLogSampler.log_file=logFile
org.apache.jmeter.protocol.http.sampler.AccessLogSampler/HTTPSampler.port=portString
#Is the following used now?
#org.apache.jmeter.protocol.http.sampler.AccessLogSampler/AccessLogSampler.generator_class_name=
#Looks to be a new field
#filterClassName
org.apache.jmeter.protocol.http.sampler.AccessLogSampler/HTTPSampler.domain=domain
org.apache.jmeter.protocol.http.sampler.AccessLogSampler/AccessLogSampler.parser_class_name=parserClassName
org.apache.jmeter.protocol.http.sampler.AccessLogSampler/HTTPSampler.image_parser=imageParsing

# Renamed class
org.apache.jmeter.protocol.jms.control.gui.JMSConfigGui=org.apache.jmeter.protocol.jms.control.gui.JMSSamplerGui

# These classes have been deleted; there's no defined replacement
org.apache.jmeter.protocol.jdbc.config.gui.SqlConfigGui=org.apache.jmeter.config.gui.ObsoleteGui
org.apache.jmeter.protocol.jms.control.gui.JndiDefaultsGui=org.apache.jmeter.config.gui.ObsoleteGui
# Should probably map to something other than ObsoleteGui...
org.apache.jmeter.threads.ReflectionThreadGroup=org.apache.jmeter.config.gui.ObsoleteGui

# Convert BSFSamplerGui
org.apache.jmeter.protocol.java.control.gui.BSFSamplerGui=org.apache.jmeter.testbeans.gui.TestBeanGUI
org.apache.jmeter.protocol.java.sampler.BSFSampler/BSFSampler.filename=filename
org.apache.jmeter.protocol.java.sampler.BSFSampler/BSFSampler.language=scriptLanguage
org.apache.jmeter.protocol.java.sampler.BSFSampler/BSFSampler.parameters=parameters
org.apache.jmeter.protocol.java.sampler.BSFSampler/BSFSampler.query=script

# Obsolete Http user Parameters modifier test element
# Note: ConfigTestElement is the test element associated with ObsoleteGui
org.apache.jmeter.protocol.http.modifier.UserParameterModifier=org.apache.jmeter.config.ConfigTestElement
org.apache.jmeter.protocol.http.modifier.gui.UserParameterModifierGui=org.apache.jmeter.config.gui.ObsoleteGui

# Obsolete Graph Full Results listener
org.apache.jmeter.visualizers.GraphAccumVisualizer=org.apache.jmeter.config.gui.ObsoleteGui
# removed in 3.0, class was deleted in r1722757
org.apache.jmeter.protocol.http.sampler.WebServiceSampler=org.apache.jmeter.config.ConfigTestElement
# removed in 3.0, class was deleted in r1722757
org.apache.jmeter.protocol.http.control.gui.WebServiceSamplerGui=org.apache.jmeter.config.gui.ObsoleteGui
# removed in 3.0, class was deleted in r1722757
org.apache.jmeter.protocol.http.modifier.ParamModifier=org.apache.jmeter.config.ConfigTestElement
# removed in 3.0, class was deleted in r1722962
org.apache.jmeter.protocol.http.modifier.ParamMask=org.apache.jmeter.config.ConfigTestElement
# removed in 3.0, class was deleted in r1722757
org.apache.jmeter.protocol.http.modifier.gui.ParamModifierGui=org.apache.jmeter.config.gui.ObsoleteGui

# removed in 3.1, class was deleted in r1774947
org.apache.jmeter.visualizers.SplineVisualizer=org.apache.jmeter.config.gui.ObsoleteGui
# removed in 3.1 class was deleted in r1763837
org.apache.jmeter.visualizers.DistributionGraphVisualizer=org.apache.jmeter.config.gui.ObsoleteGui

# removed in 3.2 class was deleted in r1771608
org.apache.jmeter.visualizers.MonitorStats=org.apache.jmeter.config.ConfigTestElement
org.apache.jmeter.visualizers.MonitorHealthVisualizer=org.apache.jmeter.config.gui.ObsoleteGui

# removed in 3.2 class was deleted in r1783280
org.apache.jmeter.protocol.http.sampler.HTTPSampler2=org.apache.jmeter.config.ConfigTestElement
org.apache.jmeter.protocol.http.sampler.SoapSampler=org.apache.jmeter.config.ConfigTestElement
org.apache.jmeter.protocol.http.control.gui.SoapSamplerGui=org.apache.jmeter.config.gui.ObsoleteGui