package com.kf.aitest.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kf.aitest.dto.DataComparisonRequestDTO;
import com.kf.aitest.dto.IndependentTaskDTO;
import com.kf.aitest.dto.IndependentTaskListDTO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据对比服务接口
 */
public interface DataComparisonService {
    
    /**
     * 创建SSE连接
     * 
     * @param taskId 任务ID
     * @return SSE发射器
     */
    SseEmitter createSseConnection(String taskId);
    
    /**
     * 开始数据对比任务（旧版本，保持向后兼容）
     *
     * @param taskId 任务ID
     * @param request 对比请求
     */
    void startComparison(String taskId, DataComparisonRequestDTO request);

    /**
     * 启动独立任务处理（新版本）
     * 将请求拆分为多个完全独立的任务，每个任务处理一个(id, stage)组合
     *
     * @param request 对比请求参数
     * @return 创建的独立任务列表
     */
    List<IndependentTaskDTO> startIndependentTasks(DataComparisonRequestDTO request);
    
    /**
     * 获取默认的UAT环境URL
     * 
     * @return UAT环境URL
     */
    String getDefaultUatUrl();
    
    /**
     * 获取默认的TEST环境URL
     *
     * @return TEST环境URL
     */
    String getDefaultTestUrl();

    /**
     * 获取独立任务详情
     *
     * @param taskId 任务ID
     * @return 独立任务详情
     */
    IndependentTaskDTO getTaskDetail(String taskId);

    /**
     * 获取独立任务列表
     *
     * @param fileMd5 文件MD5（可选，支持模糊查询）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param current 当前页码
     * @param size 每页大小
     * @return 分页结果
     */
    IPage<IndependentTaskListDTO> getIndependentTaskList(String fileMd5,
            LocalDateTime startTime, LocalDateTime endTime, Integer current, Integer size);

}
