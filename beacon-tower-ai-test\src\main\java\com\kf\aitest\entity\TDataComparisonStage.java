package com.kf.aitest.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 独立任务表实体类
 * 每条记录代表一个完全独立的任务，处理特定的(数据项, 阶段)组合
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_data_comparison_stage")
public class TDataComparisonStage extends Model<TDataComparisonStage> {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 独立任务ID（UUID）
     * 作为主键，每个任务的唯一标识
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String taskId;

    /**
     * 请求文件的MD5哈希值
     */
    @TableField("file_md5")
    private String fileMd5;

    /**
     * 阶段名称：recognize、extraction、structured、transformer
     */
    @TableField("stage_name")
    private String stageName;

    /**
     * 数据类型：json、md等
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 阶段状态：SUCCESS、FAILED、TIMEOUT
     */
    @TableField("stage_status")
    private String stageStatus;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 数据获取耗时(毫秒)
     */
    @TableField("duration")
    private Long duration;

    /**
     * 获取时间
     */
    @TableField("fetch_time")
    private LocalDateTime fetchTime;

    /**
     * AI评估结果
     */
    @TableField("ai_evaluation")
    private String aiEvaluation;

    /**
     * AI评估得分(0-100)
     */
    @TableField("ai_score")
    private Integer aiScore;

    /**
     * UAT环境数据(JSON格式)
     */
    @TableField("uat_data")
    private String uatData;

    /**
     * TEST环境数据(JSON格式)
     */
    @TableField("test_data")
    private String testData;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Override
    public Serializable pkVal() {
        return this.taskId;
    }
}
