spring.application.name=api
server.port=8004
management.server.port=7004
#
quartz.enabled=true
quartz.scheduler-name=apiScheduler
#
logging.file.path=/opt/metersphere/logs/api-test

# flyway
spring.flyway.enabled=true
spring.flyway.baseline-on-migrate=true
spring.flyway.locations=classpath:db/migration
spring.flyway.table=api_version
spring.flyway.baseline-version=0
spring.flyway.encoding=UTF-8
spring.flyway.validate-on-migrate=false
#
spring.main.allow-bean-definition-overriding=true