-- 数据库迁移脚本：移除data_id字段
-- 版本：V1.1
-- 描述：AI测试服务并发重构 - 移除dataId字段，实现真正的单条数据并发处理
-- 创建时间：2025-08-05T14:35:00+08:00

-- =====================================================
-- 备份现有数据（可选，建议在生产环境执行前先备份）
-- =====================================================

-- 创建备份表（可选）
-- CREATE TABLE t_data_comparison_stage_backup AS SELECT * FROM t_data_comparison_stage;

-- =====================================================
-- 删除相关索引
-- =====================================================

-- 删除data_id相关的索引
DROP INDEX IF EXISTS idx_data_id ON t_data_comparison_stage;

-- =====================================================
-- 删除data_id列
-- =====================================================

-- 删除data_id列
ALTER TABLE t_data_comparison_stage DROP COLUMN IF EXISTS data_id;

-- =====================================================
-- 验证表结构
-- =====================================================

-- 查看更新后的表结构（仅用于验证，实际执行时可以注释掉）
-- DESCRIBE t_data_comparison_stage;

-- =====================================================
-- 添加注释说明
-- =====================================================

-- 更新表注释，说明重构内容
ALTER TABLE t_data_comparison_stage COMMENT = '数据对比阶段结果表 - 已重构为扁平化并发处理模式，移除dataId字段';

-- =====================================================
-- 迁移完成确认
-- =====================================================

-- 插入迁移记录（如果有迁移记录表的话）
-- INSERT INTO migration_log (version, description, executed_at) 
-- VALUES ('V1.1', 'Remove data_id column for flattened concurrent processing', NOW());

-- 迁移脚本执行完成
-- 注意：此迁移不可逆，请确保在执行前已经备份数据
-- 重构后的表结构将支持真正的单条数据并发处理模式
