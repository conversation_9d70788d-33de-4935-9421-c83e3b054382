package com.kf.aitest.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 独立任务进度DTO
 * 用于SSE实时推送单个独立任务的进度信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IndependentTaskProgressDTO {
    
    /**
     * 独立任务ID
     */
    private String taskId;
    
    /**
     * 阶段名称：recognize、extraction、structured、transformer
     */
    private String stage;
    
    /**
     * 任务状态：PENDING、RUNNING、SUCCESS、FAILED、TIMEOUT
     */
    private String status;
    
    /**
     * 任务进度百分比（0-100）
     * PENDING: 0%, RUNNING: 50%, SUCCESS/FAILED/TIMEOUT: 100%
     */
    private Double progress;
    
    /**
     * 状态消息
     */
    private String message;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 错误信息（如果任务失败）
     */
    private String errorMessage;
    
    /**
     * 创建进度DTO的静态方法 - 任务开始
     */
    public static IndependentTaskProgressDTO started(String taskId, String stage) {
        IndependentTaskProgressDTO progress = new IndependentTaskProgressDTO();
        progress.setTaskId(taskId);
        progress.setStage(stage);
        progress.setStatus("RUNNING");
        progress.setProgress(50.0);
        progress.setMessage("任务正在执行: " + stage);
        progress.setUpdateTime(LocalDateTime.now());
        return progress;
    }
    
    /**
     * 创建进度DTO的静态方法 - 任务成功
     */
    public static IndependentTaskProgressDTO success(String taskId, String stage) {
        IndependentTaskProgressDTO progress = new IndependentTaskProgressDTO();
        progress.setTaskId(taskId);
        progress.setStage(stage);
        progress.setStatus("SUCCESS");
        progress.setProgress(100.0);
        progress.setMessage("任务执行成功: " + stage);
        progress.setUpdateTime(LocalDateTime.now());
        return progress;
    }
    
    /**
     * 创建进度DTO的静态方法 - 任务失败
     */
    public static IndependentTaskProgressDTO failed(String taskId, String stage, String errorMessage) {
        IndependentTaskProgressDTO progress = new IndependentTaskProgressDTO();
        progress.setTaskId(taskId);
        progress.setStage(stage);
        progress.setStatus("FAILED");
        progress.setProgress(100.0);
        progress.setMessage("任务执行失败: " + stage);
        progress.setErrorMessage(errorMessage);
        progress.setUpdateTime(LocalDateTime.now());
        return progress;
    }
    
    /**
     * 创建进度DTO的静态方法 - 任务超时
     */
    public static IndependentTaskProgressDTO timeout(String taskId, String stage) {
        IndependentTaskProgressDTO progress = new IndependentTaskProgressDTO();
        progress.setTaskId(taskId);
        progress.setStage(stage);
        progress.setStatus("TIMEOUT");
        progress.setProgress(100.0);
        progress.setMessage("任务执行超时: " + stage);
        progress.setUpdateTime(LocalDateTime.now());
        return progress;
    }
    
    /**
     * 创建进度DTO的静态方法 - 任务待执行
     */
    public static IndependentTaskProgressDTO pending(String taskId, String stage) {
        IndependentTaskProgressDTO progress = new IndependentTaskProgressDTO();
        progress.setTaskId(taskId);
        progress.setStage(stage);
        progress.setStatus("PENDING");
        progress.setProgress(0.0);
        progress.setMessage("任务等待执行: " + stage);
        progress.setUpdateTime(LocalDateTime.now());
        return progress;
    }
}
