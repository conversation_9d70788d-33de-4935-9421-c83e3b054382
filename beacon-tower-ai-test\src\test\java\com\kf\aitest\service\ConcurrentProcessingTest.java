package com.kf.aitest.service;

import com.kf.aitest.dto.DataComparisonRequestDTO;
import com.kf.aitest.dto.TaskUnit;
import com.kf.aitest.service.impl.DataComparisonServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 并发处理重构验证测试
 * 
 * 注意：这是一个基础的验证测试，主要验证重构后的数据结构和逻辑
 * 实际的集成测试需要在完整的Spring环境中运行
 */
@SpringBootTest
@ActiveProfiles("test")
public class ConcurrentProcessingTest {

    @Test
    @DisplayName("测试TaskUnit数据结构")
    public void testTaskUnitStructure() {
        // 创建测试数据
        String taskId = "test-task-001";
        String id = "data-001";
        String stage = "recognize";
        int taskIndex = 0;
        int totalTasks = 8;

        // 创建TaskUnit
        TaskUnit taskUnit = new TaskUnit(taskId, id, stage, taskIndex, totalTasks);

        // 验证基本属性
        assertEquals(taskId, taskUnit.getTaskId());
        assertEquals(id, taskUnit.getId());
        assertEquals(stage, taskUnit.getStage());
        assertEquals(taskIndex, taskUnit.getTaskIndex());
        assertEquals(totalTasks, taskUnit.getTotalTasks());

        // 验证计算方法
        assertEquals("test-task-001_data-001_recognize", taskUnit.getTaskKey());
        assertEquals(12.5, taskUnit.getProgressPercentage(), 0.01); // 1/8 * 100 = 12.5%
        assertFalse(taskUnit.isLastTask());

        // 测试最后一个任务
        TaskUnit lastTask = new TaskUnit(taskId, id, stage, 7, 8);
        assertTrue(lastTask.isLastTask());
    }

    @Test
    @DisplayName("测试任务扁平化逻辑")
    public void testTaskFlattening() {
        // 模拟请求参数
        List<String> ids = Arrays.asList("data-001", "data-002");
        List<String> stages = Arrays.asList("recognize", "extraction", "structured", "transformer");

        // 计算预期的任务数量
        int expectedTaskCount = ids.size() * stages.size(); // 2 * 4 = 8

        // 验证任务数量计算
        assertEquals(8, expectedTaskCount);

        // 验证任务组合逻辑
        int taskIndex = 0;
        for (String id : ids) {
            for (String stage : stages) {
                // 验证每个组合都会生成一个独立的任务
                String expectedTaskKey = String.format("task_%s_%s", id, stage);
                assertNotNull(expectedTaskKey);
                taskIndex++;
            }
        }
        assertEquals(expectedTaskCount, taskIndex);
    }

    @Test
    @DisplayName("测试DataComparisonRequestDTO扩展性")
    public void testRequestDTOCompatibility() {
        // 创建请求DTO
        DataComparisonRequestDTO request = new DataComparisonRequestDTO();
        request.setIds(Arrays.asList("test-001", "test-002"));
        request.setStages(Arrays.asList("recognize", "extraction"));
        request.setUserId("test-user");
        request.setEnableAiEvaluation(true);
        request.setTimeoutSeconds(60);

        // 验证基本属性
        assertNotNull(request.getIds());
        assertEquals(2, request.getIds().size());
        assertEquals(2, request.getStages().size());
        assertEquals("test-user", request.getUserId());
        assertTrue(request.getEnableAiEvaluation());
        assertEquals(60, request.getTimeoutSeconds());
    }

    @Test
    @DisplayName("测试并发处理的数据独立性")
    public void testDataIndependence() {
        // 创建多个TaskUnit，验证它们是独立的
        TaskUnit task1 = new TaskUnit("task-001", "data-001", "recognize", 0, 4);
        TaskUnit task2 = new TaskUnit("task-001", "data-001", "extraction", 1, 4);
        TaskUnit task3 = new TaskUnit("task-001", "data-002", "recognize", 2, 4);

        // 验证任务键的唯一性
        assertNotEquals(task1.getTaskKey(), task2.getTaskKey());
        assertNotEquals(task1.getTaskKey(), task3.getTaskKey());
        assertNotEquals(task2.getTaskKey(), task3.getTaskKey());

        // 验证进度计算的独立性
        assertEquals(25.0, task1.getProgressPercentage(), 0.01); // 1/4 * 100 = 25%
        assertEquals(50.0, task2.getProgressPercentage(), 0.01); // 2/4 * 100 = 50%
        assertEquals(75.0, task3.getProgressPercentage(), 0.01); // 3/4 * 100 = 75%
    }

    @Test
    @DisplayName("测试错误处理场景")
    public void testErrorHandling() {
        // 测试空参数处理
        TaskUnit emptyTask = new TaskUnit();
        assertNull(emptyTask.getTaskKey()); // 应该能处理空值

        // 测试边界条件
        TaskUnit boundaryTask = new TaskUnit("task", "id", "stage", 0, 0);
        assertEquals(0.0, boundaryTask.getProgressPercentage()); // 避免除零错误
    }

    @Test
    @DisplayName("验证重构前后的兼容性")
    public void testBackwardCompatibility() {
        // 验证API接口参数结构没有变化
        DataComparisonRequestDTO request = new DataComparisonRequestDTO();
        
        // 这些字段在重构前后都应该存在
        assertNotNull(request.getClass().getDeclaredFields());
        
        // 验证关键字段存在
        boolean hasIds = Arrays.stream(request.getClass().getDeclaredFields())
                .anyMatch(field -> "ids".equals(field.getName()));
        boolean hasStages = Arrays.stream(request.getClass().getDeclaredFields())
                .anyMatch(field -> "stages".equals(field.getName()));
        boolean hasUserId = Arrays.stream(request.getClass().getDeclaredFields())
                .anyMatch(field -> "userId".equals(field.getName()));
        
        assertTrue(hasIds, "ids字段应该存在");
        assertTrue(hasStages, "stages字段应该存在");
        assertTrue(hasUserId, "userId字段应该存在");
    }
}
