package com.kf.aitest.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 独立任务DTO
 * 用于表示一个完全独立的任务，每个任务处理一个特定的(数据项, 阶段)组合
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IndependentTaskDTO {
    
    /**
     * 独立任务ID（UUID）
     * 这是任务的唯一标识，用于SSE连接和数据库查询
     */
    private String taskId;
    
    /**
     * 文件MD5哈希值
     * 用于标识请求文件，支持按文件查询相关任务
     */
    private String fileMd5;
    
    /**
     * 阶段名称：recognize、extraction、structured、transformer
     */
    private String stage;
    
    /**
     * 任务状态：PENDING、RUNNING、SUCCESS、FAILED、TIMEOUT
     */
    private String status;
    
    /**
     * 错误信息（如果任务失败）
     */
    private String errorMessage;
    
    /**
     * AI评估结果
     */
    private String aiEvaluation;
    
    /**
     * AI评估得分(0-100)
     */
    private Integer aiScore;
    
    /**
     * 任务执行耗时(毫秒)
     */
    private Long duration;
    
    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 任务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 数据获取时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fetchTime;
    
    /**
     * UAT环境数据(JSON格式)
     */
    private String uatData;
    
    /**
     * TEST环境数据(JSON格式)
     */
    private String testData;
    
    /**
     * 数据类型：json、md等
     */
    private String dataType;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 判断任务是否成功完成
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }
    
    /**
     * 判断任务是否失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status) || "TIMEOUT".equals(status);
    }
    
    /**
     * 判断任务是否正在运行
     */
    public boolean isRunning() {
        return "RUNNING".equals(status);
    }
    
    /**
     * 判断任务是否待执行
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }
    
    /**
     * 获取任务执行进度百分比
     * PENDING: 0%, RUNNING: 50%, SUCCESS/FAILED/TIMEOUT: 100%
     */
    public double getProgressPercentage() {
        switch (status != null ? status : "PENDING") {
            case "PENDING":
                return 0.0;
            case "RUNNING":
                return 50.0;
            case "SUCCESS":
            case "FAILED":
            case "TIMEOUT":
                return 100.0;
            default:
                return 0.0;
        }
    }
    
    /**
     * 计算任务执行耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.duration = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }
}
