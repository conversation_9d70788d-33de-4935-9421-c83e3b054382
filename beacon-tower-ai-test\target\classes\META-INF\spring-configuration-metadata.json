{"groups": [{"name": "ai.ark", "type": "com.kf.aitest.configuration.ArkApiConfig", "sourceType": "com.kf.aitest.configuration.ArkApiConfig"}, {"name": "langfuse", "type": "com.kf.aitest.config.LangfuseConfig", "sourceType": "com.kf.aitest.config.LangfuseConfig"}], "properties": [{"name": "ai.ark.api-key", "type": "java.lang.String", "description": "API密钥", "sourceType": "com.kf.aitest.configuration.ArkApiConfig"}, {"name": "ai.ark.api-url", "type": "java.lang.String", "description": "API端点URL", "sourceType": "com.kf.aitest.configuration.ArkApiConfig"}, {"name": "ai.ark.default-max-tokens", "type": "java.lang.Integer", "description": "默认最大token数", "sourceType": "com.kf.aitest.configuration.ArkApiConfig"}, {"name": "ai.ark.default-temperature", "type": "java.lang.Double", "description": "默认温度参数", "sourceType": "com.kf.aitest.configuration.ArkApiConfig"}, {"name": "ai.ark.max-retries", "type": "java.lang.Integer", "description": "最大重试次数", "sourceType": "com.kf.aitest.configuration.ArkApiConfig"}, {"name": "ai.ark.model", "type": "java.lang.String", "description": "模型ID", "sourceType": "com.kf.aitest.configuration.ArkApiConfig"}, {"name": "ai.ark.timeout", "type": "java.lang.Integer", "description": "请求超时时间（毫秒）", "sourceType": "com.kf.aitest.configuration.ArkApiConfig"}, {"name": "langfuse.host", "type": "java.lang.String", "sourceType": "com.kf.aitest.config.LangfuseConfig"}, {"name": "langfuse.public-key", "type": "java.lang.String", "sourceType": "com.kf.aitest.config.LangfuseConfig"}, {"name": "langfuse.secret-key", "type": "java.lang.String", "sourceType": "com.kf.aitest.config.LangfuseConfig"}], "hints": []}