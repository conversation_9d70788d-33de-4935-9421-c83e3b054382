package com.kf.aitest.dto;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import java.util.List;

/**
 * 数据对比请求DTO
 */
@Data
public class DataComparisonRequestDTO {
    
    /**
     * 用户ID
     */
    private String userId;

    /**
     * ID列表，用于替换URL中的path数据
     */
    @NotEmpty(message = "ID列表不能为空")
    private List<String> ids;
    
    /**
     * UAT环境域名（可选，默认使用配置）
     */
    private String uatBaseUrl;
    
    /**
     * TEST环境域名（可选，默认使用配置）
     */
    private String testBaseUrl;
    
    /**
     * 是否启用AI评估（默认true）
     */
    private Boolean enableAiEvaluation = true;
    
    /**
     * 超时时间（秒，默认60秒）
     */
    private Integer timeoutSeconds = 60;

    /**
     * 是否禁用数据分片（默认true，即不启用分片）
     * 设置为true时，将发送完整数据进行AI评估，不进行分片处理
     */
    private Boolean disableChunking = true;

    /**
     * 指定要对比的阶段列表（可选）
     * 如果不指定，则对比所有阶段：["recognize", "extraction", "structured", "transformer"]
     * 可以指定部分阶段，如：["recognize", "structured"]
     */
    private List<String> stages;
}
