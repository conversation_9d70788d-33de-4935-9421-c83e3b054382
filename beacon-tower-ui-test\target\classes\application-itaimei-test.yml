server:
  port: 8001
  tomcat:
    max-swallow-size: -1
spring:
  application:
    name: beacon-tower-ui-test
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
  datasource:
    #   数据源基本配置
    username: fsuser
    password: TM@fs.456
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      validation-query: SELECT 1
  rabbitmq:
    listener:
      simple:
        retry:
          max-attempts: 1
    username: MjpyYWJiaXRtcS1zZXJ2ZXJsZXNzLWNuLWxtcjN4YWNlazAxOkxUQUk1dEdRcXh0dVhlVmtLNVNtSm9NNA==
    password: MzMxNERENDFDOTU5RDE4NzFGMzhDQzAxREU3QkJENDUyNjBENkM2OToxNzI3MjQ5MDAxNjA3
    host: test-mq-pub-01-sh2b.taimei.com
    port: 5672
    virtual-host: /
    connection-timeout: 5000
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml


