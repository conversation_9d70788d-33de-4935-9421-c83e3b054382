package com.kf.aitest.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kf.aitest.dto.IndependentTaskListDTO;
import com.kf.aitest.entity.TDataComparisonStage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据对比阶段结果表Mapper接口
 */
@Mapper
public interface TDataComparisonStageMapper extends BaseMapper<TDataComparisonStage> {

    /**
     * 根据任务ID查询所有阶段结果
     *
     * @param taskId 任务ID
     * @return 阶段结果列表
     */
    List<TDataComparisonStage> selectByTaskId(@Param("taskId") String taskId);

    /**
     * 根据任务ID和阶段名称查询阶段结果
     *
     * @param taskId 任务ID
     * @param stageName 阶段名称
     * @return 阶段结果
     */
    TDataComparisonStage selectByTaskIdAndStage(
            @Param("taskId") String taskId,
            @Param("stageName") String stageName
    );

    // 注意：selectByDataId方法已移除，因为在扁平化处理模式下不再需要按dataId查询

    /**
     * 批量插入阶段结果
     *
     * @param stageList 阶段结果列表
     * @return 插入数量
     */
    int batchInsert(@Param("stageList") List<TDataComparisonStage> stageList);

    /**
     * 统计各阶段的成功率
     *
     * @param taskId 任务ID
     * @return 统计结果
     */
    List<TDataComparisonStage> selectStageStatistics(@Param("taskId") String taskId);

    /**
     * 查询失败的阶段结果
     *
     * @param taskId 任务ID
     * @return 失败的阶段结果列表
     */
    List<TDataComparisonStage> selectFailedStages(@Param("taskId") String taskId);

    /**
     * 分页查询独立任务列表
     * 每个任务ID对应一个独立的任务记录
     *
     * @param fileMd5 文件MD5（可选，支持模糊查询）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<IndependentTaskListDTO> selectIndependentTaskList(
            @Param("fileMd5") String fileMd5,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("page") Page<IndependentTaskListDTO> page
    );
}
